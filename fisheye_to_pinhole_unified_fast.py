#!/usr/bin/env python3
"""
Fast unified Metashape script for converting fisheye cameras to pinhole views.
Includes batch processing, parallel image generation, and skip existing functionality.
"""

import Metashape
import cv2
import numpy as np
import os
import math
import time
import sys
import traceback
import concurrent.futures
from pathlib import Path

# Version check
compatible_major_version = "2.2"
found_major_version = ".".join(Metashape.app.version.split('.')[:2])
if found_major_version != compatible_major_version:
    raise Exception("Incompatible Metashape version: {} != {}".format(found_major_version, compatible_major_version))

# Try to import PySide2
PYSIDE2_AVAILABLE = False
try:
    from PySide2 import QtCore, QtWidgets, QtGui
    PYSIDE2_AVAILABLE = True
except ImportError:
    print("Warning: PySide2 library is not available. GUI mode will not work.")

# Default views
VIEWS = [
    {"name": "view_p45_r35", "yaw": 0.0, "pitch": 45.0, "roll": 35.3},
    {"name": "view_n45_r35", "yaw": 0.0, "pitch": -45.0, "roll": 35.3},
    {"name": "view_yL45_n54", "yaw": -45.0, "pitch": 0.0, "roll": -54.7}
]

def log(msg):
    """Print timestamped log message"""
    print("[{}] {}".format(time.strftime("%H:%M:%S"), msg))

def calculate_rotation_matrix(yaw_deg, pitch_deg, roll_deg):
    """Calculate rotation matrix from Euler angles (ZYX convention)"""
    yaw = math.radians(yaw_deg)
    pitch = math.radians(pitch_deg)
    roll = math.radians(roll_deg)
    
    # Individual rotation matrices
    Rz = np.array([
        [math.cos(yaw), -math.sin(yaw), 0],
        [math.sin(yaw), math.cos(yaw), 0],
        [0, 0, 1]
    ])
    
    Ry = np.array([
        [math.cos(pitch), 0, math.sin(pitch)],
        [0, 1, 0],
        [-math.sin(pitch), 0, math.cos(pitch)]
    ])
    
    Rx = np.array([
        [1, 0, 0],
        [0, math.cos(roll), -math.sin(roll)],
        [0, math.sin(roll), math.cos(roll)]
    ])
    
    # Combined rotation (ZYX order)
    R = Rz @ Ry @ Rx
    return R

def process_single_view(args):
    """Process a single view for parallel execution"""
    image_path, view, output_dir, output_size, fov_deg, fx, fy, cx, cy, k1, k2, k3, k4, width, height = args
    
    try:
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            raise Exception("Failed to load image: {}".format(image_path))
        
        # Calculate rotation
        R_view = calculate_rotation_matrix(view['yaw'], view['pitch'], view['roll'])
        
        # Setup matrices
        K_fisheye = np.array([
            [fx, 0, cx],
            [0, fy, cy],
            [0, 0, 1]
        ], dtype=np.float32)
        
        D_fisheye = np.array([k1, k2, k3, k4], dtype=np.float32)
        
        fov_rad = math.radians(fov_deg)
        pinhole_f = (output_size / 2.0) / math.tan(fov_rad / 2.0)
        K_pinhole = np.array([
            [pinhole_f, 0, output_size/2],
            [0, pinhole_f, output_size/2],
            [0, 0, 1]
        ], dtype=np.float32)
        
        # Generate undistortion maps
        map1, map2 = cv2.fisheye.initUndistortRectifyMap(
            K_fisheye,
            D_fisheye,
            R_view.astype(np.float32),
            K_pinhole,
            (output_size, output_size),
            cv2.CV_32FC1
        )
        
        # Apply remapping
        dewarped = cv2.remap(image, map1, map2, cv2.INTER_LINEAR,
                           borderMode=cv2.BORDER_CONSTANT, borderValue=(0, 0, 0))
        
        # Save image
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        new_name = "{}_{}".format(base_name, view['name'])
        new_path = os.path.join(output_dir, "images", new_name + ".jpg")
        cv2.imwrite(new_path, dewarped, [cv2.IMWRITE_JPEG_QUALITY, 95])
        
        return {
            'success': True,
            'path': new_path,
            'name': new_name,
            'view': view,
            'focal_length': pinhole_f
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'view': view
        }

def find_or_create_sensor(chunk, sensor_name, output_size, fov_deg):
    """Find existing sensor or create new one with proper calibration"""
    # Look for existing sensor
    for sensor in chunk.sensors:
        if sensor.label == sensor_name:
            return sensor
    
    # Create new sensor
    sensor = chunk.addSensor()
    sensor.label = sensor_name
    sensor.type = Metashape.Sensor.Type.Frame
    sensor.width = output_size
    sensor.height = output_size
    
    # Calculate focal length for pinhole
    fov_rad = math.radians(fov_deg)
    pinhole_fx = (output_size / 2.0) / math.tan(fov_rad / 2.0)
    sensor.focal_length = pinhole_fx
    
    # Set calibration
    calib = Metashape.Calibration()
    calib.type = Metashape.Sensor.Type.Frame
    calib.width = output_size
    calib.height = output_size
    calib.f = pinhole_fx
    calib.cx = 0  # Centered (Metashape uses offset from center)
    calib.cy = 0
    calib.b1 = 0  # No aspect ratio difference
    calib.b2 = 0
    calib.k1 = 0
    calib.k2 = 0
    calib.k3 = 0
    calib.k4 = 0
    calib.p1 = 0
    calib.p2 = 0
    sensor.calibration = calib
    
    # Also set sensor properties
    sensor.fixed = True  # Fix calibration
    sensor.pixel_width = 1
    sensor.pixel_height = 1
    
    return sensor

def process_camera_batch(camera, output_dir, output_size, fov_deg, chunk, sensor_cache, selected_views, 
                        skip_existing=False, thread_count=4):
    """Process a single camera and add its views to the chunk"""
    
    # Get calibration
    sensor = camera.sensor
    if not sensor:
        raise Exception("No sensor")
    
    calib = sensor.calibration
    if not calib:
        raise Exception("No calibration")
    
    # Extract calibration parameters
    width = sensor.width
    height = sensor.height
    
    # Get focal length and principal point
    fx = calib.f if hasattr(calib, 'f') else width/2
    fy = fx * (1 + calib.b1 if hasattr(calib, 'b1') else 0)
    cx = width/2 + (calib.cx if hasattr(calib, 'cx') else 0)
    cy = height/2 + (calib.cy if hasattr(calib, 'cy') else 0)
    
    # Get distortion
    k1 = calib.k1 if hasattr(calib, 'k1') else 0
    k2 = calib.k2 if hasattr(calib, 'k2') else 0
    k3 = calib.k3 if hasattr(calib, 'k3') else 0
    k4 = calib.k4 if hasattr(calib, 'k4') else 0
    
    # Check if image exists
    if not os.path.exists(camera.photo.path):
        raise FileNotFoundError("Image not found: {}".format(camera.photo.path))
    
    # Determine sensor name based on camera label
    if "back" in camera.label.lower():
        sensor_name = "Pinhole_Back"
    elif "front" in camera.label.lower():
        sensor_name = "Pinhole_Front"
    else:
        sensor_name = "Pinhole_Unknown"
    
    # Get or create sensor
    if sensor_name not in sensor_cache:
        sensor_cache[sensor_name] = find_or_create_sensor(chunk, sensor_name, output_size, fov_deg)
    
    pinhole_sensor = sensor_cache[sensor_name]
    
    # Prepare views to process
    views_to_process = []
    existing_views = []
    
    for view in VIEWS:
        if view['name'] not in selected_views:
            continue
            
        # Check if already exists
        base_name = os.path.splitext(os.path.basename(camera.photo.path))[0]
        new_name = "{}_{}".format(base_name, view['name'])
        new_path = os.path.join(output_dir, "images", new_name + ".jpg")
        
        # Check if already in project first
        already_in_project = False
        for cam in chunk.cameras:
            if cam.label == new_name:
                already_in_project = True
                break
        
        if already_in_project:
            continue  # Skip if already in project
            
        if skip_existing and os.path.exists(new_path):
            # File exists but not in project, add it
            existing_views.append({
                'path': new_path,
                'name': new_name,
                'view': view
            })
            continue
            
        views_to_process.append(view)
    
    # Process views in parallel
    results = []
    if views_to_process:
        args_list = []
        for view in views_to_process:
            args = (camera.photo.path, view, output_dir, output_size, fov_deg, 
                   fx, fy, cx, cy, k1, k2, k3, k4, width, height)
            args_list.append(args)
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=min(thread_count, len(views_to_process))) as executor:
            results = list(executor.map(process_single_view, args_list))
    
    # Collect successful results and existing views
    views_to_add = []
    
    # Add newly processed views
    for result in results:
        if result['success']:
            views_to_add.append({
                'path': result['path'],
                'name': result['name'],
                'view': result['view'],
                'focal_length': result['focal_length']
            })
    
    # Add existing views that need to be imported
    for existing in existing_views:
        fov_rad = math.radians(fov_deg)
        focal_length = (output_size / 2.0) / math.tan(fov_rad / 2.0)
        views_to_add.append({
            'path': existing['path'],
            'name': existing['name'],
            'view': existing['view'],
            'focal_length': focal_length
        })
    
    # Batch add all photos at once
    if views_to_add:
        # Normalize paths for consistency
        paths_to_add = [os.path.normpath(v['path']) for v in views_to_add]
        
        # Add photos and check result
        new_cams_list = chunk.addPhotos(paths_to_add)
        
        # Check if addPhotos returned None or empty list
        if new_cams_list is None:
            raise RuntimeError("addPhotos returned None for camera {}. Paths: {}".format(
                camera.label, paths_to_add))
        
        if len(new_cams_list) != len(views_to_add):
            raise RuntimeError("Failed to add all photos for camera {}. Expected {}, got {}".format(
                camera.label, len(views_to_add), len(new_cams_list)))
        
        # Configure each new camera
        for new_cam, view_data in zip(new_cams_list, views_to_add):
            new_cam.label = view_data['name']
            new_cam.sensor = pinhole_sensor
            
            # Calculate transform if original camera is aligned
            if camera.transform:
                view = view_data['view']
                R_view = calculate_rotation_matrix(view['yaw'], view['pitch'], view['roll'])
                
                T_orig = camera.transform
                R_orig = Metashape.Matrix([
                    [T_orig[0,0], T_orig[0,1], T_orig[0,2]],
                    [T_orig[1,0], T_orig[1,1], T_orig[1,2]],
                    [T_orig[2,0], T_orig[2,1], T_orig[2,2]]
                ])
                
                # When we rotate the image by R_view, we need to rotate the camera pose by R_view^T (inverse)
                R_view_inv = R_view.T  # Transpose = inverse for rotation matrices
                
                R_view_inv_meta = Metashape.Matrix([
                    [R_view_inv[0,0], R_view_inv[0,1], R_view_inv[0,2]],
                    [R_view_inv[1,0], R_view_inv[1,1], R_view_inv[1,2]],
                    [R_view_inv[2,0], R_view_inv[2,1], R_view_inv[2,2]]
                ])
                
                # New rotation
                R_new = R_orig * R_view_inv_meta
                
                # Same position
                pos = camera.center
                
                # Build transform
                T_new = Metashape.Matrix([
                    [R_new[0,0], R_new[0,1], R_new[0,2], pos.x],
                    [R_new[1,0], R_new[1,1], R_new[1,2], pos.y],
                    [R_new[2,0], R_new[2,1], R_new[2,2], pos.z],
                    [0, 0, 0, 1]
                ])
                
                new_cam.transform = T_new
    
    return len(views_to_add), results

# GUI Classes
if PYSIDE2_AVAILABLE:
    class ProcessCamerasThread(QtCore.QThread):
        """Thread for processing cameras without blocking GUI"""
        update_progress = QtCore.Signal(int, str)
        processing_finished = QtCore.Signal(bool, dict)
        
        def __init__(self, cameras_to_process, options):
            super().__init__()
            self.cameras_to_process = cameras_to_process
            self.options = options
            self._is_stopped = False
            
        def stop(self):
            self._is_stopped = True
            
        def run(self):
            start_time = time.time()
            total_cameras = len(self.cameras_to_process)
            processed_count = 0
            skipped_count = 0
            total_views_added = 0
            errors = []
            
            chunk = Metashape.app.document.chunk
            sensor_cache = {}
            
            # Create output directory
            os.makedirs(self.options['images_dir'], exist_ok=True)
            
            for i, camera in enumerate(self.cameras_to_process):
                if self._is_stopped:
                    break
                    
                progress = int((i / total_cameras) * 100)
                self.update_progress.emit(progress, "Processing camera {} ({}/{})...".format(
                    camera.label, i+1, total_cameras))
                
                try:
                    views_added, results = process_camera_batch(
                        camera, 
                        self.options['output_folder'],
                        self.options['output_size'],
                        self.options['fov_deg'],
                        chunk,
                        sensor_cache,
                        self.options['selected_views'],
                        self.options.get('skip_existing', False),
                        self.options.get('thread_count', 4)
                    )
                    
                    total_views_added += views_added
                    processed_count += 1
                    
                    # Check for any errors in results
                    for result in results:
                        if not result['success']:
                            errors.append("View {} for {}: {}".format(
                                result['view']['name'], camera.label, result['error']))
                            
                except Exception as e:
                    skipped_count += 1
                    error_msg = "Failed to process {}: {}".format(camera.label, str(e))
                    errors.append(error_msg)
                    log(error_msg)
                    
            total_time = time.time() - start_time
            
            if self._is_stopped:
                self.processing_finished.emit(False, {"message": "Processing was stopped by user."})
            else:
                self.processing_finished.emit(True, {
                    "processed": processed_count,
                    "skipped": skipped_count,
                    "total": total_cameras,
                    "views_added": total_views_added,
                    "time": total_time,
                    "errors": errors
                })
    
    class FisheyeConverterGUI(QtWidgets.QDialog):
        """Main GUI window"""
        def __init__(self):
            super().__init__()
            self.process_thread = None
            self.init_ui()
            
        def init_ui(self):
            self.setWindowTitle("Fast Fisheye to Pinhole Converter")
            self.setModal(True)
            self.resize(650, 600)
            
            layout = QtWidgets.QVBoxLayout()
            
            # Settings group
            settings_group = QtWidgets.QGroupBox("Settings")
            settings_layout = QtWidgets.QGridLayout()
            
            # Output folder
            settings_layout.addWidget(QtWidgets.QLabel("Output Folder:"), 0, 0)
            self.output_folder_edit = QtWidgets.QLineEdit()
            self.output_folder_edit.setReadOnly(True)
            settings_layout.addWidget(self.output_folder_edit, 0, 1)
            browse_btn = QtWidgets.QPushButton("Browse...")
            browse_btn.clicked.connect(self.browse_output_folder)
            settings_layout.addWidget(browse_btn, 0, 2)
            
            # Output size
            settings_layout.addWidget(QtWidgets.QLabel("Output Size (px):"), 1, 0)
            self.size_spin = QtWidgets.QSpinBox()
            self.size_spin.setRange(256, 8192)
            self.size_spin.setValue(1920)
            self.size_spin.setSingleStep(128)
            settings_layout.addWidget(self.size_spin, 1, 1)
            
            # FOV
            settings_layout.addWidget(QtWidgets.QLabel("Field of View (deg):"), 2, 0)
            self.fov_spin = QtWidgets.QDoubleSpinBox()
            self.fov_spin.setRange(30.0, 120.0)
            self.fov_spin.setValue(90.0)
            self.fov_spin.setSingleStep(5.0)
            settings_layout.addWidget(self.fov_spin, 2, 1)
            
            # Thread count
            settings_layout.addWidget(QtWidgets.QLabel("Processing Threads:"), 3, 0)
            self.thread_spin = QtWidgets.QSpinBox()
            self.thread_spin.setRange(1, 16)
            self.thread_spin.setValue(4)
            settings_layout.addWidget(self.thread_spin, 3, 1)
            
            settings_group.setLayout(settings_layout)
            layout.addWidget(settings_group)
            
            # Views group
            views_group = QtWidgets.QGroupBox("Views to Generate")
            views_layout = QtWidgets.QVBoxLayout()
            
            self.view_checkboxes = {}
            for view in VIEWS:
                cb = QtWidgets.QCheckBox("{} (Yaw:{}, Pitch:{}, Roll:{})".format(
                    view['name'], view['yaw'], view['pitch'], view['roll']))
                cb.setChecked(True)
                self.view_checkboxes[view['name']] = cb
                views_layout.addWidget(cb)
            
            views_group.setLayout(views_layout)
            layout.addWidget(views_group)
            
            # Options group
            options_group = QtWidgets.QGroupBox("Options")
            options_layout = QtWidgets.QVBoxLayout()
            
            self.skip_existing_cb = QtWidgets.QCheckBox("Skip existing images (re-align only)")
            self.skip_existing_cb.setToolTip("If images already exist on disk, skip processing and just add/align them")
            options_layout.addWidget(self.skip_existing_cb)
            
            self.disable_originals_cb = QtWidgets.QCheckBox("Disable original fisheye cameras after processing")
            self.disable_originals_cb.setChecked(True)
            self.disable_originals_cb.setToolTip("Disables fisheye cameras but keeps their tie points")
            options_layout.addWidget(self.disable_originals_cb)
            
            self.refine_alignment_cb = QtWidgets.QCheckBox("Refine alignment of new cameras")
            self.refine_alignment_cb.setToolTip("Optimize camera poses using existing tie points")
            options_layout.addWidget(self.refine_alignment_cb)
            
            options_group.setLayout(options_layout)
            layout.addWidget(options_group)
            
            # Progress group
            progress_group = QtWidgets.QGroupBox("Progress")
            progress_layout = QtWidgets.QVBoxLayout()
            
            self.status_label = QtWidgets.QLabel("Ready")
            progress_layout.addWidget(self.status_label)
            
            self.progress_bar = QtWidgets.QProgressBar()
            progress_layout.addWidget(self.progress_bar)
            
            progress_group.setLayout(progress_layout)
            layout.addWidget(progress_group)
            
            # Buttons
            button_layout = QtWidgets.QHBoxLayout()
            button_layout.addStretch()
            
            self.start_button = QtWidgets.QPushButton("Start")
            self.start_button.clicked.connect(self.start_processing)
            button_layout.addWidget(self.start_button)
            
            self.stop_button = QtWidgets.QPushButton("Stop")
            self.stop_button.clicked.connect(self.stop_processing)
            self.stop_button.setEnabled(False)
            button_layout.addWidget(self.stop_button)
            
            self.close_button = QtWidgets.QPushButton("Close")
            self.close_button.clicked.connect(self.close)
            button_layout.addWidget(self.close_button)
            
            layout.addLayout(button_layout)
            self.setLayout(layout)
            
        def browse_output_folder(self):
            folder = QtWidgets.QFileDialog.getExistingDirectory(
                self, "Select Output Folder")
            if folder:
                self.output_folder_edit.setText(folder)
                
        def start_processing(self):
            # Validate inputs
            if not self.output_folder_edit.text():
                QtWidgets.QMessageBox.warning(self, "Error", "Please select an output folder.")
                return
                
            chunk = Metashape.app.document.chunk
            if not chunk:
                QtWidgets.QMessageBox.warning(self, "Error", "No active chunk.")
                return
                
            # Find fisheye cameras
            cameras_to_process = []
            for camera in chunk.cameras:
                if (camera.sensor and 
                    camera.sensor.type == Metashape.Sensor.Type.Fisheye and
                    camera.transform and 
                    camera.photo and
                    "view_" not in camera.label):
                    cameras_to_process.append(camera)
                    
            if not cameras_to_process:
                QtWidgets.QMessageBox.warning(self, "Error", 
                    "No aligned fisheye cameras found to process.")
                return
                
            # Get selected views
            selected_views = [name for name, cb in self.view_checkboxes.items() if cb.isChecked()]
            if not selected_views:
                QtWidgets.QMessageBox.warning(self, "Error", "Please select at least one view.")
                return
                
            # Prepare options
            options = {
                'output_folder': self.output_folder_edit.text(),
                'images_dir': os.path.join(self.output_folder_edit.text(), 'images'),
                'output_size': self.size_spin.value(),
                'fov_deg': self.fov_spin.value(),
                'selected_views': selected_views,
                'skip_existing': self.skip_existing_cb.isChecked(),
                'thread_count': self.thread_spin.value()
            }
            
            # Start processing
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.close_button.setEnabled(False)
            self.progress_bar.setValue(0)
            
            self.process_thread = ProcessCamerasThread(cameras_to_process, options)
            self.process_thread.update_progress.connect(self.on_update_progress)
            self.process_thread.processing_finished.connect(self.on_processing_finished)
            self.process_thread.start()
            
        def stop_processing(self):
            if self.process_thread:
                self.process_thread.stop()
                self.status_label.setText("Stopping...")
                self.stop_button.setEnabled(False)
                
        def on_update_progress(self, progress, status):
            self.progress_bar.setValue(progress)
            self.status_label.setText(status)
            
        def on_processing_finished(self, success, stats):
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.close_button.setEnabled(True)
            
            if not success:
                QtWidgets.QMessageBox.warning(self, "Aborted", stats["message"])
                self.status_label.setText("Aborted")
                return
                
            # Post-processing
            chunk = Metashape.app.document.chunk
            post_errors = []
            
            # Disable originals
            if self.disable_originals_cb.isChecked():
                disabled_count = 0
                for camera in chunk.cameras:
                    if camera.sensor and camera.sensor.type == Metashape.Sensor.Type.Fisheye:
                        camera.enabled = False
                        disabled_count += 1
                        
            # Refine alignment
            if self.refine_alignment_cb.isChecked():
                self.status_label.setText("Refining alignment...")
                QtWidgets.QApplication.processEvents()
                
                new_cams = [c for c in chunk.cameras if "view_" in c.label and c.enabled]
                if new_cams:
                    try:
                        chunk.alignCameras(cameras=new_cams, reset_alignment=False)
                    except Exception as e:
                        post_errors.append("Alignment refinement failed: {}".format(str(e)))
                        
            # Show results
            message = "Processing complete!\n\n"
            message += "Cameras processed: {}\n".format(stats['processed'])
            if 'views_added' in stats:
                message += "Views added: {}\n".format(stats['views_added'])
            message += "Cameras skipped: {}\n".format(stats['skipped'])
            message += "Total time: {:.1f} seconds\n".format(stats['time'])
            message += "Processing speed: {:.1f} views/second\n".format(
                stats.get('views_added', 0) / stats['time'] if stats['time'] > 0 else 0)
            
            all_errors = stats.get('errors', []) + post_errors
            if all_errors:
                message += "\nErrors encountered:\n"
                for error in all_errors[:5]:  # Show first 5 errors
                    message += "- {}\n".format(error)
                if len(all_errors) > 5:
                    message += "... and {} more errors".format(len(all_errors) - 5)
                    
            QtWidgets.QMessageBox.information(self, "Complete", message)
            self.status_label.setText("Complete")
            self.progress_bar.setValue(100)
            
        def closeEvent(self, event):
            if self.process_thread and self.process_thread.isRunning():
                reply = QtWidgets.QMessageBox.question(
                    self, 'Confirm Exit',
                    "Processing is still running. Are you sure you want to exit?",
                    QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
                    QtWidgets.QMessageBox.No)
                    
                if reply == QtWidgets.QMessageBox.Yes:
                    self.stop_processing()
                    self.process_thread.wait(1000)
                    event.accept()
                else:
                    event.ignore()
            else:
                event.accept()

def show_gui():
    """Show the GUI dialog"""
    if not PYSIDE2_AVAILABLE:
        Metashape.app.messageBox("PySide2 is required for the GUI version. Please use the non-GUI script instead.")
        return
        
    app = QtWidgets.QApplication.instance()
    if app is None:
        app = QtWidgets.QApplication([])
        
    dialog = FisheyeConverterGUI()
    dialog.exec_()

def main():
    """Main entry point - show GUI if available, otherwise run console version"""
    if PYSIDE2_AVAILABLE:
        show_gui()
    else:
        print("PySide2 not available. Please install it or use the non-GUI version of this script.")

# Add menu item to Metashape
label = "Scripts/Fast Fisheye to Pinhole Converter"
Metashape.app.addMenuItem(label, main)

if __name__ == "__main__":
    main()