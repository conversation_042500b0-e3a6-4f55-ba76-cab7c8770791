# Unsuccessful Attempts to Create Tie Points for Pinhole Views

## Overview
The main challenge has been creating tie points for pinhole cameras after converting from fisheye images while preserving the camera poses inherited from the fisheye alignment. Despite the pinhole images containing essentially the same visual data as the fisheyes (just undistorted), all attempts to create tie points have resulted in 0 points.

## Failed Approaches

### 1. **Single Unified Sensor Approach**
- **What I tried**: Used a single sensor ("Pinhole_Unified") for all pinhole cameras instead of separate Front/Back sensors
- **Theory**: Multiple sensors might interfere with tie point creation
- **Result**: Failed - still 0 tie points
- **Why it failed**: Sensor grouping wasn't the issue

### 2. **Removing Camera Groups**
- **What I tried**: Removed camera group assignments entirely
- **Theory**: Camera groups might make Metashape treat cameras as rigidly connected
- **Result**: Failed - still 0 tie points
- **Why it failed**: Groups weren't preventing matching

### 3. **Project Save Before Matching**
- **What I tried**: Added `doc.save()` before matchPhotos to ensure cameras were "registered"
- **Theory**: Newly added cameras might need to be saved first
- **Result**: TERRIBLE - overwrote user's project file after deleting fisheyes!
- **Why it failed**: Not only unnecessary but destructive

### 4. **Simplified matchPhotos Parameters**
- **What I tried**: Called matchPhotos() with minimal/no parameters
- **Theory**: Let Metashape use its defaults like manual alignment
- **Result**: Failed - still 0 tie points
- **Why it failed**: Parameters weren't the issue

### 5. **Representative Camera Matching**
- **What I tried**: Complex logic to match only one "representative" view from each camera position
- **Theory**: Views from same position can't create tie points (no parallax)
- **Result**: Failed - misunderstood the problem entirely
- **Why it was wrong**: User correctly pointed out the pinhole views are from DIFFERENT camera positions and should match fine

### 6. **Pre-removal Tie Point Creation**
- **What I tried**: Created tie points BEFORE removing fisheye cameras
- **Theory**: Chunk might be in better state with all cameras present
- **Result**: Failed - still 0 tie points
- **Why it failed**: Timing wasn't the issue

### 7. **Various matchPhotos Parameter Combinations**
- **What I tried**: Multiple combinations of parameters:
  - `downscale` values (0, 1, 2)
  - `generic_preselection` (True/False)
  - `reference_preselection` (True/False)  
  - Different keypoint and tiepoint limits
  - `guided_matching` on/off
  - `reset_matches` variations
- **Result**: All failed - still 0 tie points

### 8. **Disabling Fisheye Cameras During Matching**
- **What I tried**: Temporarily disabled fisheye cameras while matching pinhole cameras
- **Theory**: Prevent fisheyes from interfering with pinhole matching
- **Result**: Failed - still 0 tie points

### 9. **Using cameras Parameter**
- **What I tried**: `chunk.matchPhotos(cameras=pinhole_cameras)`
- **Theory**: Explicitly tell Metashape which cameras to match
- **Result**: Failed - still 0 tie points

### 10. **alignCameras After matchPhotos**
- **What I tried**: Called alignCameras with `reset_alignment=True`
- **Result**: TERRIBLE - reset all camera poses, defeating the entire purpose
- **Why it failed**: Completely misunderstood that we need to preserve poses

## Critical Bugs Introduced

### 1. **optimizeCameras Without Tie Points**
- **Bug**: Called optimizeCameras when there were no tie points
- **Result**: Caused pinhole cameras to lose their alignment entirely
- **Fix**: Added check for tie points before optimization

### 2. **Incorrect Error Messages**
- **Bug**: Told users that views from same position can't create tie points
- **Result**: Confused users who correctly understood their pinhole views should match
- **Fix**: Removed misleading messages

## Key Insights

1. **Manual alignment works**: User confirmed that manually running "Workflow > Align Photos" after the script successfully creates tie points and finds poses. This proves:
   - The pinhole images are valid
   - Tie points CAN be created between them
   - The issue is specifically with how the script calls matchPhotos

2. **Console warnings**: The repeated "Warning: Can't resume matching without keypoints" suggests newly added cameras don't have keypoints detected yet

3. **The fundamental issue remains unsolved**: Despite all attempts, the script's matchPhotos() call fails to create tie points that manual alignment successfully creates

## What We Know Works
- Image conversion from fisheye to pinhole
- Camera pose inheritance from fisheye alignment  
- Mask processing and application
- Manual tie point creation through Metashape's UI

## What Still Doesn't Work
- Programmatic tie point creation via matchPhotos() for the newly added pinhole cameras