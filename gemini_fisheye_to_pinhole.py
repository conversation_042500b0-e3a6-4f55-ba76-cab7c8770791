# filename: convert_fisheye_to_perspective_views.py
#
# This is a python script for Agisoft Metashape Pro.
#
# Script Goal:
# Converts a Metashape project with aligned circular or full-frame fisheye cameras
# into a project where each fisheye camera is replaced by three pre-defined perspective views.
# This version uses OpenCV for dewarping to ensure geometric fidelity with external tools.
#
# Author: <PERSON><PERSON><PERSON><PERSON>
# Version: 1.2.1
#
# How to install:
# 1. Place this script in your Metashape scripts folder
#    (e.g., C:/Users/<USER>/AppData/Local/Agisoft/Metashape Pro/scripts/ on Windows).
# 2. Restart Metashape.
#
# How to use:
# 1. Open a Metashape project with aligned fisheye cameras.
# 2. Run the script from the "Scripts" menu.
# 3. Use the GUI to configure the output folder, resolution, views to generate, and other options.
# 4. Click "Start" to begin the conversion process.

import Metashape
import os
import sys
import time
import traceback
import math
import concurrent.futures
from pathlib import Path

# --- Compatibility and Dependency Check ---

compatible_major_version = "2.2"
found_major_version = ".".join(Metashape.app.version.split('.')[:2])
if found_major_version != compatible_major_version:
    print(f"Warning: Incompatible Metashape version. Found {found_major_version}, expected {compatible_major_version}. Script may not work as expected.")

PYSIDE2_AVAILABLE = False
try:
    from PySide2 import QtCore, QtWidgets
    PYSIDE2_AVAILABLE = True
except ImportError:
    print("Warning: PySide2 library is not available. The GUI for this script cannot be launched.")

try:
    import cv2
    import numpy as np
except ImportError:
    raise ImportError("This script requires 'opencv-python' and 'numpy'. Please install them in Metashape's Python environment.")

# === Core Conversion Logic ===

def calculate_dewarp_rotation_matrix(yaw_deg, pitch_deg, roll_deg):
    import numpy as np
    yaw, pitch, roll = math.radians(yaw_deg), math.radians(pitch_deg), math.radians(roll_deg)
    Rz = np.array([[math.cos(yaw),-math.sin(yaw),0],[math.sin(yaw),math.cos(yaw),0],[0,0,1]])
    Ry = np.array([[math.cos(pitch),0,math.sin(pitch)],[0,1,0],[-math.sin(pitch),0,math.cos(pitch)]])
    Rx = np.array([[1,0,0],[0,math.cos(roll),-math.sin(roll)],[0,math.sin(roll),math.cos(roll)]])
    return Rz @ Ry @ Rx

def convert_single_fisheye(camera: Metashape.Camera, options: dict):
    try:
        output_folder, mask_folder = options['output_folder'], options.get('mask_folder')
        camera_label, fisheye_image_path = camera.label, camera.photo.path
        
        img_data = camera.photo.image()
        if not img_data: raise ValueError("Could not read image data.")
        
        image_bytes = img_data.convert("RGB", "U8").tostring()
        fisheye_image_np = np.frombuffer(image_bytes, dtype=np.uint8).reshape(camera.sensor.height, camera.sensor.width, 3)

        fisheye_mask_np = None
        if mask_folder:
            mask_base = os.path.splitext(os.path.basename(fisheye_image_path))[0]
            for ext in ['.png', '.jpg', '.jpeg', '.tif', '.tiff']:
                pmp = os.path.join(mask_folder, mask_base + ext)
                if os.path.exists(pmp):
                    fisheye_mask_np = cv2.imread(pmp, cv2.IMREAD_GRAYSCALE)
                    if fisheye_mask_np is None: print(f"Warning: Failed to load mask {pmp}")
                    break
        
        calib = camera.sensor.calibration
        K_fe = np.array([[calib.f, 0, calib.cx], [0, calib.f, calib.cy], [0, 0, 1]], dtype=np.float32)
        D_fe = np.array([calib.k1, calib.k2, calib.k3, calib.k4], dtype=np.float32)

        base_name, orig_ext = os.path.splitext(os.path.basename(fisheye_image_path))
        view_results = {}

        for view_name, view_params in options['target_views'].items():
            if view_name not in options['selected_views']: continue

            fov_rad = math.radians(view_params['fov_deg'])
            persp_fx = (options['output_width'] / 2.0) / math.tan(fov_rad / 2.0)
            persp_fy = (options['output_height'] / 2.0) / math.tan(fov_rad / 2.0)
            K_new = np.array([[persp_fx,0,(options['output_width']-1)/2.0], [0,persp_fy,(options['output_height']-1)/2.0], [0,0,1]], dtype=np.float32)

            R_persp_to_fisheye = calculate_dewarp_rotation_matrix(view_params["yaw"], view_params["pitch"], view_params["roll"])
            map1, map2 = cv2.fisheye.initUndistortRectifyMap(K_fe, D_fe, R_persp_to_fisheye, K_new, (options['output_width'], options['output_height']), cv2.CV_16SC2)
            perspective_image = cv2.remap(fisheye_image_np, map1, map2, cv2.INTER_LINEAR, borderMode=cv2.BORDER_CONSTANT, borderValue=(0,0,0))
            
            new_img_filename = f"{base_name}_{view_name}{orig_ext}"
            new_img_path = os.path.join(output_folder, new_img_filename)
            cv2.imwrite(new_img_path, cv2.cvtColor(perspective_image, cv2.COLOR_RGB2BGR))

            new_mask_path = None
            if fisheye_mask_np is not None:
                dewarped_mask = cv2.remap(fisheye_mask_np, map1, map2, cv2.INTER_NEAREST, borderMode=cv2.BORDER_CONSTANT, borderValue=0)
                mask_output_fname = f"{os.path.splitext(new_img_filename)[0]}.png"
                new_mask_path = os.path.join(output_folder, "masks", mask_output_fname)
                os.makedirs(os.path.dirname(new_mask_path), exist_ok=True)
                cv2.imwrite(new_mask_path, dewarped_mask)

            T_original = camera.transform
            R_relative_np = R_persp_to_fisheye.T 
            R_relative = Metashape.Matrix.inv(Metashape.Matrix([R_relative_np[0], R_relative_np[1], R_relative_np[2]]))
            T_new = T_original * Metashape.Matrix.Rotation(R_relative)

            view_results[view_name] = {
                "new_image_path": new_img_path, "new_mask_path": new_mask_path, "new_transform": T_new,
                "new_calibration_params": {"f": K_new[0,0], "cx": K_new[0,2], "cy": K_new[1,2]},
            }
        return {"camera_label": camera_label, "views": view_results, "error": None}
    except Exception as e:
        print(f"ERROR processing camera {camera_label}: {e}"); traceback.print_exc()
        return {"camera_label": camera_label, "views": None, "error": str(e)}

if PYSIDE2_AVAILABLE:
    class ProcessFisheyeThread(QtCore.QThread):
        update_progress = QtCore.Signal(int, str)
        processing_finished = QtCore.Signal(bool, dict)
        def __init__(self, cameras, options): super().__init__(); self.cameras, self.options, self._is_stopped = cameras, options, False
        def stop(self): self._is_stopped = True
        def run(self):
            start_time, total, converted_ok, errors, results = time.time(), len(self.cameras), 0, [], []
            self.update_progress.emit(0, f"Starting conversion of {total} cameras...")
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.options['camera_threads']) as executor:
                future_map = {executor.submit(convert_single_fisheye, cam, self.options): cam for cam in self.cameras}
                for i, future in enumerate(concurrent.futures.as_completed(future_map)):
                    if self._is_stopped: break
                    try:
                        res = future.result();
                        if res['error']: raise Exception(res['error'])
                        results.append(res); converted_ok += 1
                    except Exception as e: errors.append(f"Conversion failed for {future_map[future].label}: {e}")
                    progress = int(((i + 1) / total) * 50)
                    self.update_progress.emit(progress, f"Converting images... ({i + 1}/{total})")
            if self._is_stopped: self.processing_finished.emit(False, {"message": "Processing aborted."}); return
            self.update_progress.emit(50, "Adding new cameras to project...")
            chunk, new_cameras = Metashape.app.document.chunk, []
            if results:
                for i, data in enumerate(results):
                    if self._is_stopped: break
                    progress = 50 + int(((i + 1) / len(results)) * 40)
                    self.update_progress.emit(progress, f"Adding cameras for {data['camera_label']}...")
                    for view_name, view_data in data['views'].items():
                        try:
                            cam = chunk.addCamera(); cam.label = f"{data['camera_label']}_{view_name}"; cam.transform = view_data['new_transform']
                            sensor = next((s for s in chunk.sensors if s.type==Metashape.Sensor.Frame and s.width==self.options['output_width'] and s.height==self.options['output_height']), None)
                            if not sensor:
                                sensor = chunk.addSensor(); sensor.type = Metashape.Sensor.Type.Frame
                                sensor.width, sensor.height = self.options['output_width'], self.options['output_height']; sensor.label = f"P_{sensor.width}x{sensor.height}"
                            cam.sensor = sensor
                            params = view_data['new_calibration_params']; calib = cam.sensor.calibration
                            calib.f, calib.cx, calib.cy = params['f'], params['cx'], params['cy']; cam.sensor.calibration = calib
                            cam.photo.path = view_data['new_image_path']
                            if view_data['new_mask_path']: cam.mask = Metashape.Mask(); cam.mask.load(view_data['new_mask_path'])
                            new_cameras.append(cam)
                        except Exception as e: errors.append(f"Failed to add camera {data['camera_label']}_{view_name}: {e}")
            if self._is_stopped: self.processing_finished.emit(False, {"message": "Processing aborted."}); return
            self.update_progress.emit(90, "Post-processing...");
            if self.options['disable_originals']:
                for cam in self.cameras: cam.enabled = False
            if self.options['realign_new'] and new_cameras:
                try: chunk.alignCameras(cameras=new_cameras, reset_alignment=False)
                except Exception as e: errors.append(f"Refining alignment failed: {e}")
            self.update_progress.emit(100, "Finished.")
            stats = {"processed": converted_ok, "skipped": total-converted_ok, "total": total, "time": time.time() - start_time, "errors": errors}
            self.processing_finished.emit(True, stats)

    class FisheyeConverterGUI(QtWidgets.QMainWindow):
        def __init__(self):
            super().__init__(); self.process_thread = None; self.cpu_count = os.cpu_count() or 1; self.init_ui()
        def init_ui(self):
            self.setWindowTitle("Fisheye to Perspective Converter"); self.setGeometry(100, 100, 600, 500)
            cw=QtWidgets.QWidget(); self.setCentralWidget(cw); ml=QtWidgets.QVBoxLayout(cw)
            sg=QtWidgets.QGroupBox("Settings"); sl=QtWidgets.QVBoxLayout(); fl=QtWidgets.QHBoxLayout()
            fl.addWidget(QtWidgets.QLabel("Output Folder:")); self.output_folder_path = QtWidgets.QLabel("<i>Not selected</i>"); fl.addWidget(self.output_folder_path, 1)
            bb=QtWidgets.QPushButton("Browse..."); bb.clicked.connect(self.select_output_folder); fl.addWidget(bb); sl.addLayout(fl)
            mfl=QtWidgets.QHBoxLayout(); mfl.addWidget(QtWidgets.QLabel("Masks Folder (Optional):")); self.mask_folder_path = QtWidgets.QLabel("<i>None</i>"); mfl.addWidget(self.mask_folder_path, 1)
            bmb=QtWidgets.QPushButton("Browse..."); bmb.clicked.connect(self.select_mask_folder); mfl.addWidget(bmb); sl.addLayout(mfl)
            res_layout=QtWidgets.QHBoxLayout(); res_layout.addWidget(QtWidgets.QLabel("Output Height:")); self.height_spinbox=QtWidgets.QSpinBox(); self.height_spinbox.setRange(256, 8192); self.height_spinbox.setValue(1920); self.height_spinbox.setSingleStep(128); res_layout.addWidget(self.height_spinbox)
            res_layout.addWidget(QtWidgets.QLabel("FoV (deg):")); self.fov_spinbox = QtWidgets.QDoubleSpinBox(); self.fov_spinbox.setRange(40.0, 140.0); self.fov_spinbox.setValue(90.0); res_layout.addWidget(self.fov_spinbox); sl.addLayout(res_layout)
            thread_layout=QtWidgets.QHBoxLayout(); thread_layout.addWidget(QtWidgets.QLabel("Camera Threads:")); self.camera_thread_spinner=QtWidgets.QSpinBox(); self.camera_thread_spinner.setRange(1, self.cpu_count); self.camera_thread_spinner.setValue(max(1, self.cpu_count // 2)); thread_layout.addWidget(self.camera_thread_spinner); sl.addLayout(thread_layout)
            sg.setLayout(sl); ml.addWidget(sg)
            vg=QtWidgets.QGroupBox("Views to Generate"); vl=QtWidgets.QHBoxLayout()
            self.view_checkboxes = {"view_p45_r35": QtWidgets.QCheckBox("P+45,R+35"), "view_n45_r35": QtWidgets.QCheckBox("P-45,R+35"), "view_yL45_n54": QtWidgets.QCheckBox("Y-45,R-55")}
            for cb in self.view_checkboxes.values(): cb.setChecked(True); vl.addWidget(cb)
            vg.setLayout(vl); ml.addWidget(vg)
            post_group=QtWidgets.QGroupBox("Post-Processing"); post_layout=QtWidgets.QVBoxLayout()
            self.disable_originals_cb = QtWidgets.QCheckBox("Disable original fisheye cameras"); self.realign_new_cb = QtWidgets.QCheckBox("Refine alignment of new cameras");
            post_layout.addWidget(self.disable_originals_cb); post_layout.addWidget(self.realign_new_cb); post_group.setLayout(post_layout); ml.addWidget(post_group)
            prg=QtWidgets.QGroupBox("Progress"); prl=QtWidgets.QVBoxLayout()
            self.status_label=QtWidgets.QLabel("Ready."); self.progress_bar=QtWidgets.QProgressBar(); prl.addWidget(self.status_label); prl.addWidget(self.progress_bar); prg.setLayout(prl); ml.addWidget(prg)
            bl=QtWidgets.QHBoxLayout(); self.start_button=QtWidgets.QPushButton("Start"); self.start_button.clicked.connect(self.start_processing)
            self.stop_button=QtWidgets.QPushButton("Stop"); self.stop_button.clicked.connect(self.stop_processing); self.stop_button.setEnabled(False)
            self.close_button=QtWidgets.QPushButton("Close"); self.close_button.clicked.connect(self.close)
            bl.addStretch(); bl.addWidget(self.start_button); bl.addWidget(self.stop_button); bl.addWidget(self.close_button); ml.addLayout(bl)
        def select_output_folder(self): f=QtWidgets.QFileDialog.getExistingDirectory(self, "Select Output Folder");
            if f: self.output_folder_path.setText(f)
        def select_mask_folder(self): f=QtWidgets.QFileDialog.getExistingDirectory(self, "Select Masks Folder");
            if f: self.mask_folder_path.setText(f)
        def start_processing(self):
            chunk = Metashape.app.document.chunk
            if not chunk: QtWidgets.QMessageBox.warning(self, "Error", "No active chunk."); return
            if self.output_folder_path.text() == "<i>Not selected</i>": QtWidgets.QMessageBox.warning(self, "Error", "Please select an output folder."); return
            cams = [c for c in chunk.cameras if c.enabled and c.type == Metashape.Camera.Type.Regular and c.sensor.type == Metashape.Sensor.Type.Fisheye]
            if not cams: QtWidgets.QMessageBox.warning(self, "Error", "No enabled fisheye cameras found."); return
            sv = [name for name, cb in self.view_checkboxes.items() if cb.isChecked()]
            if not sv: QtWidgets.QMessageBox.warning(self, "Error", "Please select at least one view."); return
            options = {"output_folder": self.output_folder_path.text(), "mask_folder": self.mask_folder_path.text() if self.mask_folder_path.text() != "<i>None</i>" else None,
                       "output_height": self.height_spinbox.value(), "output_width": self.height_spinbox.value(), "camera_threads": self.camera_thread_spinner.value(),
                       "selected_views": sv, "disable_originals": self.disable_originals_cb.isChecked(), "realign_new": self.realign_new_cb.isChecked(),
                       "target_views": {"view_p45_r35": {"yaw":0.0,"pitch":45.0,"roll":35.3,"fov_deg":self.fov_spinbox.value()},
                                        "view_n45_r35": {"yaw":0.0,"pitch":-45.0,"roll":35.3,"fov_deg":self.fov_spinbox.value()},
                                        "view_yL45_n54": {"yaw":-45.0,"pitch":0.0,"roll":-54.7,"fov_deg":self.fov_spinbox.value()}}}
            self.start_button.setEnabled(False); self.stop_button.setEnabled(True); self.progress_bar.setValue(0)
            self.process_thread = ProcessFisheyeThread(cams, options)
            self.process_thread.update_progress.connect(self.on_update_progress)
            self.process_thread.processing_finished.connect(self.on_processing_finished)
            self.process_thread.start()
        def stop_processing(self):
            if self.process_thread: self.process_thread.stop(); self.status_label.setText("Stopping..."); self.stop_button.setEnabled(False)
        def on_update_progress(self, p, s): self.progress_bar.setValue(p); self.status_label.setText(s)
        def on_processing_finished(self, success, stats):
            self.start_button.setEnabled(True); self.stop_button.setEnabled(False)
            msg = f"Finished!\n\nTotal: {stats['total']}\nProcessed: {stats['processed']}\nSkipped/Errors: {stats['skipped']}\nTime: {stats['time']:.2f}s"
            if stats['errors']: msg += "\n\nErrors:\n" + "\n".join(stats['errors'][:5])
            if success: QtWidgets.QMessageBox.information(self, "Success", msg); self.status_label.setText("Finished.")
            else: QtWidgets.QMessageBox.warning(self, "Aborted", stats["message"]); self.status_label.setText("Aborted.")
        def closeEvent(self, event):
            if self.process_thread and self.process_thread.isRunning():
                reply = QtWidgets.QMessageBox.question(self,'Confirm Exit',"Processing active. Are you sure you want to exit?", QtWidgets.QMessageBox.Yes|QtWidgets.QMessageBox.No, QtWidgets.QMessageBox.No)
                if reply == QtWidgets.QMessageBox.Yes: self.stop_processing(); self.process_thread.wait(500); event.accept()
                else: event.ignore()
            else: event.accept()

def main_entry_point():
    if not PYSIDE2_AVAILABLE:
        msg = "GUI could not be launched because PySide2 is not available."
        print(msg)
        try: Metashape.app.messageBox(msg)
        except: pass
        return

    try:
        app = QtWidgets.QApplication.instance() or QtWidgets.QApplication(sys.argv)
        global main_dialog 
        main_dialog = FisheyeConverterGUI()
        main_dialog.show()
    except Exception as e:
        print(f"An error occurred while launching the GUI: {e}"); traceback.print_exc()
        try: Metashape.app.messageBox(f"An error occurred: {e}")
        except: pass

if 'Metashape' in sys.modules:
    label = "Scripts/Convert Fisheye to Perspective Views"
    Metashape.app.addMenuItem(label, main_entry_point)
    print(f"To execute this script, go to '{label}' in the menu.")