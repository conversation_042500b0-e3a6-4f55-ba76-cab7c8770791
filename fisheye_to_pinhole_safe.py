# Fisheye to Pinhole Conversion - Safe Version
# This version is extra careful not to modify existing cameras

import Metashape
import os
import math
import numpy as np
import cv2
import time

def log(message):
    """Print with timestamp"""
    print("[{}] {}".format(time.strftime("%H:%M:%S"), message))

class SafeFisheyeConverter:
    def __init__(self):
        self.output_size = 1920
        self.fov_deg = 90.0
        self.jpeg_quality = 95
        
    def process(self, chunk, output_dir):
        """Process fisheye cameras to pinhole"""
        
        # 1. First, let's document what we have
        log("=== Current project state ===")
        
        # Count cameras by group
        sensor_groups = {}
        aligned_count = 0
        
        for camera in chunk.cameras:
            sensor_name = camera.sensor.label if camera.sensor else "No sensor"
            if sensor_name not in sensor_groups:
                sensor_groups[sensor_name] = {"total": 0, "aligned": 0}
            
            sensor_groups[sensor_name]["total"] += 1
            if camera.transform:
                sensor_groups[sensor_name]["aligned"] += 1
                aligned_count += 1
        
        log("Total cameras: {}".format(len(chunk.cameras)))
        log("Aligned cameras: {}".format(aligned_count))
        log("Camera groups:")
        for name, counts in sensor_groups.items():
            log("  {}: {} cameras ({} aligned)".format(
                name, counts["total"], counts["aligned"]))
        
        # 2. Find cameras to process (only aligned ones)
        cameras_to_process = []
        for camera in chunk.cameras:
            if camera.transform and camera.photo:
                # Check if this is an original camera (not already processed)
                if "view_" not in camera.label:
                    cameras_to_process.append(camera)
        
        log("\nFound {} original aligned cameras to process".format(len(cameras_to_process)))
        
        if not cameras_to_process:
            log("No cameras to process!")
            return False
        
        # 3. Create output directories
        images_dir = os.path.join(output_dir, "images")
        os.makedirs(images_dir, exist_ok=True)
        
        # 4. Process and save images ONLY
        log("\n=== Processing images ===")
        processed_images = []
        
        for idx, camera in enumerate(cameras_to_process):
            log("Processing {} ({}/{})".format(
                camera.label, idx + 1, len(cameras_to_process)))
            
            # Load image
            if not os.path.exists(camera.photo.path):
                log("  WARNING: Image not found: {}".format(camera.photo.path))
                continue
                
            image = cv2.imread(camera.photo.path)
            if image is None:
                log("  WARNING: Failed to load image")
                continue
            
            # Get calibration
            calib = self.extract_calibration(camera)
            if not calib:
                log("  WARNING: No calibration")
                continue
            
            # Process each view
            views = [
                {"name": "view_p45_r35", "yaw": 0.0, "pitch": 45.0, "roll": 35.3},
                {"name": "view_n45_r35", "yaw": 0.0, "pitch": -45.0, "roll": 35.3},
                {"name": "view_yL45_n54", "yaw": -45.0, "pitch": 0.0, "roll": -54.7}
            ]
            
            for view in views:
                # Calculate rotation
                R_view = self.calculate_rotation_matrix(
                    view['yaw'], view['pitch'], view['roll'])
                
                # Dewarp
                dewarped = self.dewarp_fisheye(image, calib, R_view)
                
                # Save
                base_name = os.path.splitext(os.path.basename(camera.photo.path))[0]
                new_name = "{}_{}".format(base_name, view['name'])
                new_path = os.path.join(images_dir, new_name + ".jpg")
                cv2.imwrite(new_path, dewarped, [cv2.IMWRITE_JPEG_QUALITY, self.jpeg_quality])
                
                # Store info
                processed_images.append({
                    'original_camera': camera,
                    'view': view,
                    'path': new_path,
                    'name': new_name
                })
        
        log("\nProcessed {} images".format(len(processed_images)))
        
        # 5. Ask user to confirm before adding to project
        if not Metashape.app.getBool("Add {} cameras to project?".format(len(processed_images))):
            log("User cancelled")
            return False
        
        # 6. Add cameras to project VERY CAREFULLY
        log("\n=== Adding cameras to project ===")
        
        # Group by original sensor
        by_sensor = {}
        for img_info in processed_images:
            sensor = img_info['original_camera'].sensor
            sensor_label = sensor.label if sensor else "Unknown"
            
            if sensor_label not in by_sensor:
                by_sensor[sensor_label] = []
            by_sensor[sensor_label].append(img_info)
        
        # Add cameras for each sensor group
        for sensor_label, images in by_sensor.items():
            log("\nProcessing sensor group: {} ({} images)".format(
                sensor_label, len(images)))
            
            # Create new sensor
            new_sensor = chunk.addSensor()
            new_sensor.label = "Pinhole_{}".format(sensor_label)
            new_sensor.type = Metashape.Sensor.Type.Frame
            new_sensor.width = self.output_size
            new_sensor.height = self.output_size
            
            # Calculate focal length
            fov_rad = math.radians(self.fov_deg)
            focal = (self.output_size / 2.0) / math.tan(fov_rad / 2.0)
            new_sensor.focal_length = focal
            
            # Set calibration
            calib = Metashape.Calibration()
            calib.width = self.output_size
            calib.height = self.output_size
            calib.f = focal
            calib.cx = 0  # Center
            calib.cy = 0
            calib.k1 = 0
            calib.k2 = 0
            calib.k3 = 0
            sensor.calibration = calib
            
            # Collect paths
            paths = [img['path'] for img in images]
            
            # Count cameras before adding
            cameras_before = len(chunk.cameras)
            
            # Add photos
            chunk.addPhotos(paths)
            
            # Count cameras after adding
            cameras_after = len(chunk.cameras)
            cameras_added = cameras_after - cameras_before
            
            log("  Added {} cameras (expected {})".format(
                cameras_added, len(paths)))
            
            if cameras_added != len(paths):
                log("  WARNING: Camera count mismatch!")
            
            # Configure the newly added cameras
            for i, img_info in enumerate(images):
                if cameras_before + i < len(chunk.cameras):
                    new_cam = chunk.cameras[cameras_before + i]
                    new_cam.label = img_info['name']
                    new_cam.sensor = new_sensor
                    
                    # Calculate transform
                    orig_cam = img_info['original_camera']
                    orig_T = orig_cam.transform
                    
                    # Extract rotation from original
                    R_orig = Metashape.Matrix([
                        [orig_T[0,0], orig_T[0,1], orig_T[0,2]],
                        [orig_T[1,0], orig_T[1,1], orig_T[1,2]],
                        [orig_T[2,0], orig_T[2,1], orig_T[2,2]]
                    ])
                    
                    # View rotation
                    view = img_info['view']
                    R_view_np = self.calculate_rotation_matrix(
                        view['yaw'], view['pitch'], view['roll'])
                    
                    R_view = Metashape.Matrix([
                        [R_view_np[0,0], R_view_np[0,1], R_view_np[0,2]],
                        [R_view_np[1,0], R_view_np[1,1], R_view_np[1,2]],
                        [R_view_np[2,0], R_view_np[2,1], R_view_np[2,2]]
                    ])
                    
                    # New rotation
                    R_new = R_orig * R_view
                    
                    # Build transform (same position as original)
                    pos = orig_cam.center
                    T_new = Metashape.Matrix([
                        [R_new[0,0], R_new[0,1], R_new[0,2], pos.x],
                        [R_new[1,0], R_new[1,1], R_new[1,2], pos.y],
                        [R_new[2,0], R_new[2,1], R_new[2,2], pos.z],
                        [0, 0, 0, 1]
                    ])
                    
                    new_cam.transform = T_new
        
        # 7. Final report
        log("\n=== Final state ===")
        
        # Recount
        sensor_groups_after = {}
        for camera in chunk.cameras:
            sensor_name = camera.sensor.label if camera.sensor else "No sensor"
            if sensor_name not in sensor_groups_after:
                sensor_groups_after[sensor_name] = {"total": 0, "aligned": 0}
            
            sensor_groups_after[sensor_name]["total"] += 1
            if camera.transform:
                sensor_groups_after[sensor_name]["aligned"] += 1
        
        log("Total cameras now: {}".format(len(chunk.cameras)))
        log("Camera groups:")
        for name, counts in sensor_groups_after.items():
            log("  {}: {} cameras ({} aligned)".format(
                name, counts["total"], counts["aligned"]))
        
        log("\nDONE - Remember to save manually if results look good!")
        return True
    
    def calculate_rotation_matrix(self, yaw_deg, pitch_deg, roll_deg):
        """Calculate rotation matrix from Euler angles"""
        yaw = math.radians(yaw_deg)
        pitch = math.radians(pitch_deg) 
        roll = math.radians(roll_deg)
        
        Rz = np.array([
            [math.cos(yaw), -math.sin(yaw), 0],
            [math.sin(yaw), math.cos(yaw), 0],
            [0, 0, 1]
        ])
        
        Ry = np.array([
            [math.cos(pitch), 0, math.sin(pitch)],
            [0, 1, 0],
            [-math.sin(pitch), 0, math.cos(pitch)]
        ])
        
        Rx = np.array([
            [1, 0, 0],
            [0, math.cos(roll), -math.sin(roll)],
            [0, math.sin(roll), math.cos(roll)]
        ])
        
        return Rz @ Ry @ Rx
    
    def extract_calibration(self, camera):
        """Extract calibration safely"""
        try:
            sensor = camera.sensor
            if not sensor:
                return None
                
            width = sensor.width
            height = sensor.height
            
            # Get calibration
            if sensor.calibration:
                calib = sensor.calibration
                fx = calib.f if hasattr(calib, 'f') else width/2
                fy = fx * (1 + calib.b1 if hasattr(calib, 'b1') else 0)
                cx = width/2 + (calib.cx if hasattr(calib, 'cx') else 0)
                cy = height/2 + (calib.cy if hasattr(calib, 'cy') else 0)
                k1 = calib.k1 if hasattr(calib, 'k1') else 0
                k2 = calib.k2 if hasattr(calib, 'k2') else 0
                k3 = calib.k3 if hasattr(calib, 'k3') else 0
                k4 = calib.k4 if hasattr(calib, 'k4') else 0
            else:
                fx = fy = width/2
                cx = width/2
                cy = height/2
                k1 = k2 = k3 = k4 = 0
            
            return {
                'width': width, 'height': height,
                'fx': fx, 'fy': fy, 'cx': cx, 'cy': cy,
                'k1': k1, 'k2': k2, 'k3': k3, 'k4': k4
            }
        except:
            return None
    
    def dewarp_fisheye(self, image, calib, R_view):
        """Dewarp fisheye to perspective"""
        # Camera matrices
        K_fisheye = np.array([
            [calib['fx'], 0, calib['cx']],
            [0, calib['fy'], calib['cy']],
            [0, 0, 1]
        ], dtype=np.float32)
        
        D_fisheye = np.array([
            calib['k1'], calib['k2'], 
            calib['k3'], calib['k4']
        ], dtype=np.float32)
        
        # Pinhole matrix
        fov_rad = math.radians(self.fov_deg)
        f = (self.output_size / 2.0) / math.tan(fov_rad / 2.0)
        K_pinhole = np.array([
            [f, 0, self.output_size/2],
            [0, f, self.output_size/2],
            [0, 0, 1]
        ], dtype=np.float32)
        
        # Generate maps
        map1, map2 = cv2.fisheye.initUndistortRectifyMap(
            K_fisheye, D_fisheye,
            R_view.astype(np.float32),
            K_pinhole,
            (self.output_size, self.output_size),
            cv2.CV_32FC1
        )
        
        # Remap
        return cv2.remap(image, map1, map2, cv2.INTER_LINEAR,
                        borderMode=cv2.BORDER_CONSTANT)

def main():
    doc = Metashape.app.document
    if not doc or not doc.chunk:
        print("No active document/chunk")
        return
        
    output_dir = Metashape.app.getExistingDirectory("Select output directory")
    if not output_dir:
        return
    
    converter = SafeFisheyeConverter()
    
    # Get settings
    # Get individual values since getMultipleStrings doesn't exist
    output_size = Metashape.app.getString("Output size (pixels):", "1920")
    if not output_size:
        return
    
    fov = Metashape.app.getString("Field of view (degrees):", "90")
    if not fov:
        return
        
    quality = Metashape.app.getString("JPEG quality (1-100):", "95")
    if not quality:
        return
    
    settings = [output_size, fov, quality]
    
    if settings:
        try:
            converter.output_size = int(settings[0])
            converter.fov_deg = float(settings[1])
            converter.jpeg_quality = int(settings[2])
        except:
            print("Invalid settings")
            return
    else:
        return
    
    # Process
    converter.process(doc.chunk, output_dir)

if __name__ == "__main__":
    main()