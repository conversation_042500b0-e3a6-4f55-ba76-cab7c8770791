# filename: gemini_fisheye_to_pinhole_v2.py
#
# This is a python script for Agisoft Metashape Pro.
#
# Script Goal:
# Converts a Metashape project with aligned fisheye cameras into a project
# where each fisheye camera is replaced by three pre-defined perspective views.
#
# Author: <PERSON><PERSON><PERSON><PERSON> & <PERSON>
# Version: 3.3 - Rewrote processing loop to use a batch 'addPhotos' call, fixing critical runtime error.
#
# How to install:
# 1. Place this script in your Metashape scripts folder
#    (e.g., C:/Users/<USER>/AppData/Local/Agisoft/Metashape Pro/scripts/ on Windows).
# 2. Restart Metashape.
#
# How to use:
# 1. Open a Metashape project with aligned fisheye cameras.
# 2. Run the script from the "Tools" -> "Scripts" menu.
# 3. Use the GUI to configure the output folder, resolution, views to generate, and other options.
# 4. Click "Start" to begin the conversion process.

import Metashape
import os
import sys
import time
import traceback
import math
from pathlib import Path

# --- Compatibility and Dependency Check ---
compatible_major_version = "2.2"
found_major_version = ".".join(Metashape.app.version.split('.')[:2])
if found_major_version != compatible_major_version:
    raise Exception(f"Incompatible Metashape version: {found_major_version} != {compatible_major_version}")

PYSIDE2_AVAILABLE = False
try:
    from PySide2 import QtCore, QtWidgets
    PYSIDE2_AVAILABLE = True
except ImportError:
    print("Warning: PySide2 library is not available. The GUI for this script cannot be launched.")

try:
    import cv2
    import numpy as np
except ImportError:
    raise ImportError("This script requires 'opencv-python' and 'numpy'. Please install them in Metashape's Python environment.")

# === Core Logic ===

VIEWS = [
    {"name": "view_p45_r35", "yaw": 0.0, "pitch": 45.0, "roll": 35.3},
    {"name": "view_n45_r35", "yaw": 0.0, "pitch": -45.0, "roll": 35.3},
    {"name": "view_yL45_n54", "yaw": -45.0, "pitch": 0.0, "roll": -54.7}
]

def calculate_rotation_matrix(yaw_deg, pitch_deg, roll_deg):
    """Calculate rotation matrix from Euler angles (ZYX convention)"""
    yaw = math.radians(yaw_deg)
    pitch = math.radians(pitch_deg)
    roll = math.radians(roll_deg)
    Rz = np.array([[math.cos(yaw),-math.sin(yaw),0],[math.sin(yaw),math.cos(yaw),0],[0,0,1]])
    Ry = np.array([[math.cos(pitch),0,math.sin(pitch)],[0,1,0],[-math.sin(pitch),0,math.cos(pitch)]])
    Rx = np.array([[1,0,0],[0,math.cos(roll),-math.sin(roll)],[0,math.sin(roll),math.cos(roll)]])
    return Rz @ Ry @ Rx

# === GUI and Threading ===

if PYSIDE2_AVAILABLE:
    class ProcessCamerasThread(QtCore.QThread):
        update_progress = QtCore.Signal(int, str)
        processing_finished = QtCore.Signal(bool, dict)

        def __init__(self, cameras_to_process, options):
            super().__init__()
            self.cameras_to_process = cameras_to_process
            self.options = options
            self._is_stopped = False

        def stop(self):
            self._is_stopped = True
            
        def run(self):
            start_time = time.time()
            total_cameras = len(self.cameras_to_process)
            processed_count = 0
            skipped_count = 0
            errors = []
            
            chunk = Metashape.app.document.chunk
            sensor_cache = {}

            for i, camera in enumerate(self.cameras_to_process):
                if self._is_stopped:
                    break

                progress = int(((i) / total_cameras) * 100)
                self.update_progress.emit(progress, f"Processing camera {camera.label} ({i+1}/{total_cameras})...")
                Metashape.app.update()

                try:
                    # --- Stage 1: Create all view data for one camera ---
                    views_to_add = []
                    
                    sensor = camera.sensor
                    calib = sensor.calibration
                    fx, fy = calib.f, calib.f * (1 + (calib.b1 if hasattr(calib, 'b1') else 0))
                    cx, cy = sensor.width/2 + calib.cx, sensor.height/2 + calib.cy
                    k1, k2, k3, k4 = calib.k1, calib.k2, calib.k3, calib.k4
                    K_fe = np.array([[fx, 0, cx], [0, fy, cy], [0, 0, 1]], dtype=np.float32)
                    D_fe = np.array([k1, k2, k3, k4], dtype=np.float32)

                    if not os.path.exists(camera.photo.path):
                        raise FileNotFoundError(f"Source image not found: {camera.photo.path}")
                    image_np = cv2.imread(camera.photo.path)
                    if image_np is None:
                        raise IOError(f"Failed to load image: {camera.photo.path}")

                    for view_params in self.options['target_views']:
                        view_name = view_params['name']
                        if view_name not in self.options['selected_views']:
                            continue
                        
                        R_view = calculate_rotation_matrix(view_params["yaw"], view_params["pitch"], view_params["roll"])
                        pinhole_f = (self.options['output_width'] / 2.0) / math.tan(math.radians(self.options['fov_deg'] / 2.0))
                        K_pinhole = np.array([[pinhole_f, 0, self.options['output_width']/2.0], [0, pinhole_f, self.options['output_height']/2.0], [0, 0, 1]], dtype=np.float32)
                        
                        map1, map2 = cv2.fisheye.initUndistortRectifyMap(K_fe, D_fe, R_view, K_pinhole, (self.options['output_width'], self.options['output_height']), cv2.CV_16SC2)
                        dewarped = cv2.remap(image_np, map1, map2, cv2.INTER_LINEAR, borderMode=cv2.BORDER_CONSTANT, borderValue=(0,0,0))
                        
                        base_name, orig_ext = os.path.splitext(os.path.basename(camera.photo.path))
                        new_img_filename = f"{base_name}_{view_name}{orig_ext}"
                        new_img_path = str(Path(self.options['output_folder']) / new_img_filename)
                        cv2.imwrite(new_img_path, dewarped)
                        
                        T_orig, R_orig, pos = camera.transform, camera.transform.rotation(), camera.center
                        R_view_meta = Metashape.Matrix([[R_view[0,0],R_view[0,1],R_view[0,2]], [R_view[1,0],R_view[1,1],R_view[1,2]], [R_view[2,0],R_view[2,1],R_view[2,2]]])
                        R_new = R_orig * R_view_meta
                        T_new = Metashape.Matrix([[R_new[0,0],R_new[0,1],R_new[0,2],pos.x], [R_new[1,0],R_new[1,1],R_new[1,2],pos.y], [R_new[2,0],R_new[2,1],R_new[2,2],pos.z], [0,0,0,1]])

                        views_to_add.append({"path": new_img_path, "label": new_img_filename.rsplit('.', 1)[0], "transform": T_new, "focal_length": pinhole_f})

                    # --- Stage 2: Add all views for this camera in a single batch ---
                    if views_to_add:
                        paths_to_add = [view['path'] for view in views_to_add]
                        new_cams_list = chunk.addPhotos(paths_to_add)
                        if len(new_cams_list) != len(views_to_add):
                            raise RuntimeError("Failed to add all photos for camera " + camera.label)

                        sensor_key = f"Pinhole_{self.options['output_width']}x{self.options['output_height']}_{self.options['fov_deg']:.1f}deg"
                        if sensor_key not in sensor_cache:
                            sensor = chunk.addSensor()
                            sensor.label = sensor_key
                            sensor.type = Metashape.Sensor.Type.Frame
                            sensor.width = self.options['output_width']
                            sensor.height = self.options['output_height']
                            calib = sensor.calibration
                            calib.f = views_to_add[0]["focal_length"]
                            calib.cx, calib.cy = 0, 0
                            sensor.calibration = calib
                            sensor_cache[sensor_key] = sensor
                        
                        for new_cam, view_data in zip(new_cams_list, views_to_add):
                            new_cam.label = view_data["label"]
                            new_cam.transform = view_data["transform"]
                            new_cam.sensor = sensor_cache[sensor_key]
                    
                    processed_count += 1
                except Exception as e:
                    skipped_count += 1
                    error_msg = f"Failed to process {camera.label}: {e}"
                    errors.append(error_msg)
                    print(error_msg)
                    traceback.print_exc()

            total_time = time.time() - start_time
            if self._is_stopped:
                self.processing_finished.emit(False, {"message": "Processing was stopped by user."})
            else:
                self.processing_finished.emit(True, {"processed": processed_count, "skipped": skipped_count, "total": total_cameras, "time": total_time, "errors": errors})

    class FisheyeConverterGUI(QtWidgets.QMainWindow):
        def __init__(self):
            super().__init__()
            self.process_thread = None
            self.init_ui()

        def init_ui(self):
            self.setWindowTitle("Fisheye to Perspective Converter")
            self.setGeometry(100, 100, 600, 450)
            
            central_widget = QtWidgets.QWidget()
            self.setCentralWidget(central_widget)
            main_layout = QtWidgets.QVBoxLayout(central_widget)

            settings_group = QtWidgets.QGroupBox("Settings")
            settings_layout = QtWidgets.QVBoxLayout()
            folder_layout = QtWidgets.QHBoxLayout()
            folder_layout.addWidget(QtWidgets.QLabel("Output Folder:"))
            self.output_folder_path = QtWidgets.QLabel("<i>Not selected</i>")
            folder_layout.addWidget(self.output_folder_path, 1)
            browse_button = QtWidgets.QPushButton("Browse...")
            browse_button.clicked.connect(self.select_output_folder)
            folder_layout.addWidget(browse_button)
            settings_layout.addLayout(folder_layout)

            res_layout = QtWidgets.QHBoxLayout()
            res_layout.addWidget(QtWidgets.QLabel("Output Size (px):"))
            self.size_spinbox = QtWidgets.QSpinBox()
            self.size_spinbox.setRange(256, 8192); self.size_spinbox.setValue(1920); self.size_spinbox.setSingleStep(128)
            res_layout.addWidget(self.size_spinbox)
            res_layout.addWidget(QtWidgets.QLabel("FoV (deg):"))
            self.fov_spinbox = QtWidgets.QDoubleSpinBox()
            self.fov_spinbox.setRange(40.0, 140.0); self.fov_spinbox.setValue(90.0)
            res_layout.addWidget(self.fov_spinbox)
            settings_layout.addLayout(res_layout)
            settings_group.setLayout(settings_layout)
            main_layout.addWidget(settings_group)
            
            views_group = QtWidgets.QGroupBox("Views to Generate")
            views_layout = QtWidgets.QHBoxLayout()
            self.view_checkboxes = {
                "view_p45_r35": QtWidgets.QCheckBox("Pitch:+45, Roll:+35.3"),
                "view_n45_r35": QtWidgets.QCheckBox("Pitch:-45, Roll:+35.3"),
                "view_yL45_n54": QtWidgets.QCheckBox("Yaw:-45, Roll:-54.7")
            }
            for cb in self.view_checkboxes.values():
                cb.setChecked(True)
                views_layout.addWidget(cb)
            views_group.setLayout(views_layout)
            main_layout.addWidget(views_group)

            post_group = QtWidgets.QGroupBox("Post-Processing")
            post_layout = QtWidgets.QVBoxLayout()
            self.disable_originals_cb = QtWidgets.QCheckBox("Disable original fisheye cameras")
            self.disable_originals_cb.setChecked(True)
            self.realign_new_cb = QtWidgets.QCheckBox("Refine alignment of new cameras")
            post_layout.addWidget(self.disable_originals_cb)
            post_layout.addWidget(self.realign_new_cb)
            post_group.setLayout(post_layout)
            main_layout.addWidget(post_group)

            progress_group = QtWidgets.QGroupBox("Progress")
            progress_layout = QtWidgets.QVBoxLayout()
            self.status_label = QtWidgets.QLabel("Ready.")
            self.progress_bar = QtWidgets.QProgressBar()
            progress_layout.addWidget(self.status_label)
            progress_layout.addWidget(self.progress_bar)
            progress_group.setLayout(progress_layout)
            main_layout.addWidget(progress_group)

            buttons_layout = QtWidgets.QHBoxLayout()
            self.start_button = QtWidgets.QPushButton("Start")
            self.start_button.clicked.connect(self.start_processing)
            self.stop_button = QtWidgets.QPushButton("Stop")
            self.stop_button.clicked.connect(self.stop_processing)
            self.stop_button.setEnabled(False)
            self.close_button = QtWidgets.QPushButton("Close")
            self.close_button.clicked.connect(self.close)
            buttons_layout.addStretch(); buttons_layout.addWidget(self.start_button)
            buttons_layout.addWidget(self.stop_button); buttons_layout.addWidget(self.close_button)
            main_layout.addLayout(buttons_layout)
            
        def select_output_folder(self):
            folder = QtWidgets.QFileDialog.getExistingDirectory(self, "Select Output Folder")
            if folder:
                self.output_folder_path.setText(str(Path(folder)))

        def start_processing(self):
            chunk = Metashape.app.document.chunk
            if not chunk: QtWidgets.QMessageBox.warning(self, "Error", "No active chunk."); return
            if self.output_folder_path.text() == "<i>Not selected</i>": QtWidgets.QMessageBox.warning(self, "Error", "Please select an output folder."); return
            
            cams = [c for c in chunk.cameras if c.enabled and c.transform and c.photo and c.sensor.type == Metashape.Sensor.Type.Fisheye]
            
            if not cams: QtWidgets.QMessageBox.warning(self, "Error", "No aligned fisheye cameras found."); return
            
            sv = [name for name, cb in self.view_checkboxes.items() if cb.isChecked()]
            if not sv: QtWidgets.QMessageBox.warning(self, "Error", "Please select at least one view."); return
            
            options = {
                "output_folder": self.output_folder_path.text(),
                "output_height": self.size_spinbox.value(),
                "output_width": self.size_spinbox.value(),
                "fov_deg": self.fov_spinbox.value(),
                "selected_views": sv,
                "target_views": VIEWS
            }
            
            self.start_button.setEnabled(False); self.stop_button.setEnabled(True); self.progress_bar.setValue(0)
            self.process_thread = ProcessCamerasThread(cams, options)
            self.process_thread.update_progress.connect(self.on_update_progress)
            self.process_thread.processing_finished.connect(self.on_processing_finished)
            self.process_thread.start()

        def stop_processing(self):
            if self.process_thread:
                self.process_thread.stop()
                self.status_label.setText("Stopping...")
                self.stop_button.setEnabled(False)

        def on_update_progress(self, p, s):
            self.progress_bar.setValue(p)
            self.status_label.setText(s)
        
        def on_processing_finished(self, success, stats):
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            
            if not success:
                QtWidgets.QMessageBox.warning(self, "Aborted", stats["message"])
                self.status_label.setText("Aborted.")
                return
            
            self.on_update_progress(100, "Post-processing..."); QtWidgets.QApplication.processEvents()
            
            chunk = Metashape.app.document.chunk
            if self.disable_originals_cb.isChecked():
                for cam in chunk.cameras:
                    if cam.sensor.type == Metashape.Sensor.Type.Fisheye:
                        cam.enabled = False

            if self.realign_new_cb.isChecked():
                new_cams = [c for c in chunk.cameras if "view_" in c.label]
                if new_cams:
                    try:
                        chunk.alignCameras(cameras=new_cams, reset_alignment=False)
                    except Exception as e:
                        stats["errors"].append(f"Refining alignment failed: {e}")

            message = (f"Finished!\n\n"
                       f"Total Fisheye Cameras: {stats['total']}\n"
                       f"Processed: {stats['processed']}\n"
                       f"Skipped/Errors: {stats['skipped']}\n"
                       f"Time: {stats['time']:.2f}s")
            
            if stats['errors']:
                message += "\n\nErrors:\n" + "\n".join(stats['errors'][:5])
            
            QtWidgets.QMessageBox.information(self, "Success", message)
            self.status_label.setText("Finished.")
            
        def closeEvent(self, event):
            if self.process_thread and self.process_thread.isRunning():
                reply = QtWidgets.QMessageBox.question(self, 'Confirm Exit', "Processing active. Are you sure?", QtWidgets.QMessageBox.Yes|QtWidgets.QMessageBox.No, QtWidgets.QMessageBox.No)
                if reply == QtWidgets.QMessageBox.Yes: self.stop_processing(); self.process_thread.wait(500); event.accept()
                else: event.ignore()
            else: event.accept()

def main_entry_point():
    if not PYSIDE2_AVAILABLE:
        msg = "GUI could not be launched because PySide2 is not available."
        print(msg)
        try: Metashape.app.messageBox(msg)
        except: pass
        return
    try:
        app = QtWidgets.QApplication.instance() or QtWidgets.QApplication(sys.argv)
        global main_dialog 
        main_dialog = FisheyeConverterGUI()
        main_dialog.show()
    except Exception as e:
        print(f"An error occurred while launching the GUI: {e}"); traceback.print_exc()
        try: Metashape.app.messageBox(f"An error occurred: {e}")
        except: pass

if 'Metashape' in sys.modules and hasattr(Metashape, 'app'):
    label = "Scripts/Convert Fisheye to Perspective Views"
    Metashape.app.addMenuItem(label, main_entry_point)
    print(f"To execute this script, go to '{label}' in the 'Tools' menu.")