# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Structure

This repository contains Python scripts for Agisoft Metashape Pro, a photogrammetry software. The codebase is organized into:

- `metashape-scripts-master/`: Official Agisoft scripts repository with version compatibility checks
- `Metashape/`: Custom scripts for specific 3D scanning workflows
- Root directory: Additional conversion and processing scripts

## Script Architecture

All Metashape scripts follow these patterns:

### Version Compatibility Check
Scripts include version compatibility checks for Metashape:
```python
compatible_major_version = "2.2"
found_major_version = ".".join(Metashape.app.version.split('.')[:2])
if found_major_version != compatible_major_version:
    raise Exception("Incompatible Metashape version: {} != {}".format(found_major_version, compatible_major_version))
```

### Core Workflow Pattern
Most scripts follow this structure:
1. Import Metashape and required libraries
2. Version compatibility check
3. Access document/chunk: `doc = Metashape.app.document` or `doc = Metashape.Document()`
4. Process operations (alignment, optimization, building models)
5. Save document: `doc.save()`

### GUI Dialog Scripts
Scripts with user interfaces use PySide2:
- Inherit from `QtWidgets.QDialog`
- Use grid layouts for form controls
- Connect signals/slots for button interactions
- Register with Metashape menu: `Metashape.app.addMenuItem(label, function)`

### Script Categories

**Workflow Scripts**: Complete processing pipelines (samples/general_workflow.py)
- Photo matching, camera alignment, depth map generation
- Model building, UV mapping, texture generation
- Export to various formats (OBJ, LAS, orthomosaics)

**Optimization Scripts**: Camera and tie point filtering
- Reprojection error filtering
- Reconstruction uncertainty filtering
- Image count and projection accuracy filtering

**Utility Scripts**: Specialized tasks
- Reference coordinate system import/export
- Region control and bounding box management
- Batch processing configurations (XML)

## Running Scripts

Scripts can be executed:
1. Through Metashape Pro GUI menu after installation
2. As command-line arguments for batch processing
3. Via XML batch job configurations for automated workflows

## Dependencies

- Metashape Pro 2.2+ (primary dependency)
- PySide2 for GUI components
- numpy for mathematical operations (auto-installed via pip_install)
- Standard Python libraries (os, sys, time, math)

## Custom Script Integration

User-contributed scripts should be placed in `src/contrib/` directory and follow the established patterns for version checking and error handling.