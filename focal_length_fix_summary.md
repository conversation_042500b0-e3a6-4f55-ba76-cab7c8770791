# Focal Length Fix Summary

## Problem
The focal length was being set to 3137.85816 instead of the expected 960 pixels for a 90° FOV at 1920x1920 resolution.

## Root Cause
The script was only setting the calibration's focal length (`calib.f`) but not the sensor's `focal_length` property. Metashape requires BOTH values to be set for the focal length to be properly respected.

## Solution
Added the following critical changes to `fisheye_to_pinhole_unified_improved.py`:

1. **Set sensor.focal_length property** (line 116):
   ```python
   sensor.focal_length = pinhole_fx
   ```

2. **Set calibration type** (line 98):
   ```python
   calib.type = Metashape.Sensor.Type.Frame
   ```

3. **Set pixel dimensions** (lines 119-120):
   ```python
   sensor.pixel_width = 1
   sensor.pixel_height = 1
   ```

4. **Updated sensor verification** to check both `calibration.f` and `sensor.focal_length` when looking for existing sensors.

## Technical Details
- Expected focal length calculation: `f = (1920 / 2) / tan(45°) = 960`
- The issue occurred because Metashape uses the sensor's `focal_length` property as the primary value and may override the calibration if only `calib.f` is set.
- Both values must be consistent for proper operation.

## Verification
After these changes, new pinhole cameras should have the correct focal length of 960 pixels for 90° FOV at 1920x1920 resolution.