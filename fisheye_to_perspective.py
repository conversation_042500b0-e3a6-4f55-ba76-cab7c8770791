# Fisheye to Perspective Image Conversion for Metashape
# Converts fisheye images to multiple perspective views while maintaining camera alignment

import os
import sys
import time
import traceback
import Metashape
import subprocess
import concurrent.futures
import locale
import json
import datetime
import gc
import numpy as np
import math

# === Part 1: Imports and Helper Functions ===

# Simple logging function
def log_message(message):
    """Simple logging function with timestamp."""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{timestamp} {message}")

# Debug messages
log_message("Starting script...")
log_message(f"Python version: {sys.version}")
log_message(f"Metashape path: {os.path.dirname(sys.executable)}")

# Global variable for GUI window
gui_window = None

# Global language variable
LANG = 'en'  # Default to English

# Translation dictionary
translations = {
    "en": {
        "script_title": "Fisheye to Perspective Image Conversion",
        "version": "Version",
        "checking_libraries": "Checking required libraries...",
        "library_installed": "Library {0} is already installed.",
        "library_not_found": "Library {0} not found.",
        "opencv_imported": "OpenCV successfully imported, version: {0}",
        "failed_import_opencv": "Failed to import OpenCV: {0}",
        "script_cant_work": "Script cannot work without OpenCV.",
        "press_enter": "Press Enter to exit...",
        "error_title": "Error",
        "info_title": "Information",
        "warning_title": "Warning",
        "no_chunk_error": "No active chunk found.",
        "no_fisheye_cameras": "No fisheye cameras found in the project.",
        "found_cameras": "Found {0} fisheye cameras.",
        "processing_camera": "Processing camera {0} ({1}/{2})",
        "camera_processed": "Camera {0} processed successfully",
        "processing_error": "Error processing camera {0}: {1}",
        "processing_complete": "Processing complete",
        "processing_aborted": "Processing aborted by user.",
        "select_output_folder": "Select output folder for images",
        "no_output_folder": "Please select an output folder.",
        "fisheye_model_label": "Fisheye model:",
        "output_folder_label": "Output folder:",
        "not_selected": "Not selected",
        "browse_button": "Browse...",
        "perspective_count_label": "Number of perspective views:",
        "perspective_fov_label": "Field of view (degrees):",
        "overlap_label": "Overlap (degrees):",
        "image_size_label": "Output image size:",
        "start_button": "Start",
        "stop_button": "Stop",
        "close_button": "Close",
        "settings_group": "Settings",
        "image_group": "Image Parameters",
        "project_info_group": "Project Information",
        "cameras_count_label": "Fisheye cameras found:",
        "progress_group": "Processing Progress",
        "current_camera_label": "Current camera:",
        "status_label": "Status:",
        "time_remaining_label": "Time remaining:",
        "no_camera": "None",
        "waiting_start": "Waiting to start",
        "processing_status": "Processing...",
        "completed_status": "Completed",
        "aborted_status": "Aborted",
        "error_status": "Error",
        "time_format": "--:--:--",
        "post_processing_group": "Post-processing",
        "realign_cameras": "Realign cameras after creation",
        "remove_fisheye": "Remove original fisheye cameras",
        "generate_masks": "Generate masks for valid areas",
        "mask_generated": "Mask generated for {0}",
        "adding_perspective_cameras": "Adding perspective cameras...",
        "camera_added": "Added camera {0}",
        "setting_camera_params": "Setting camera parameters...",
        "equidistant": "Equidistant",
        "stereographic": "Stereographic", 
        "orthographic": "Orthographic",
        "equisolid": "Equisolid angle",
        "processing_finished": "Processing finished successfully",
        "views_arranged": "Views arranged in {0} configuration",
        "horizontal": "horizontal",
        "vertical": "vertical",
        "grid": "grid",
        "custom": "custom",
        "arrangement_label": "View arrangement:",
        "detecting_fisheye_model": "Detecting fisheye model from camera...",
        "fisheye_model_detected": "Detected fisheye model: {0}",
        "using_default_model": "Using default fisheye model: {0}",
        "creating_perspective_view": "Creating perspective view {0}",
        "perspective_params": "Perspective {0}: yaw={1}°, pitch={2}°",
        "conversion_complete": "Conversion complete for {0}",
        "total_time": "Total time: {0}",
        "images_saved": "Images saved to: {0}",
        "mask_threshold_label": "Mask threshold:",
        "interpolation_label": "Interpolation:",
        "nearest": "Nearest",
        "linear": "Linear", 
        "cubic": "Cubic",
        "file_format_label": "File format:",
        "quality_label": "Quality:",
        "threads_label": "Processing threads:",
        "detecting_calibration": "Detecting camera calibration...",
        "calibration_detected": "Calibration detected: f={0}, k1={1}",
        "no_calibration": "No calibration found, using defaults",
        "creating_projection_map": "Creating projection map...",
        "applying_remap": "Applying remap transformation...",
        "saving_image": "Saving image: {0}",
        "save_error": "Error saving image: {0}",
        "invalid_image": "Invalid or empty image for {0}",
        "fisheye_fov_label": "Fisheye FOV (degrees):",
        "auto_detect": "Auto-detect",
        "sensor_created": "Created sensor {0}",
        "calibration_set": "Set calibration for {0}",
        "transform_set": "Set transform for {0}",
        "metadata_updated": "Updated metadata for {0}",
        "console_mode": "Running in console mode",
        "gui_mode": "Running in GUI mode",
        "pyqt5_available": "PyQt5 is available",
        "pyqt5_unavailable": "PyQt5 is not available: {0}",
        "init_gui": "Initializing GUI...",
        "gui_launched": "GUI launched successfully",
        "console_select_model": "Select fisheye model",
        "console_select_views": "Number of perspective views (1-12)",
        "console_select_fov": "Field of view in degrees (10-120)",
        "console_select_overlap": "Overlap in degrees (0-30)",
        "console_select_size": "Output image size",
        "console_processing": "Processing {0} fisheye cameras...",
        "batch_progress": "Batch progress: {0}/{1}",
        "realigning_cameras": "Realigning cameras...",
        "removing_fisheye_cameras": "Removing fisheye cameras...",
        "alignment_complete": "Camera alignment complete",
        "removal_complete": "Fisheye camera removal complete"
    }
}

# Function to get localized string
def _(text_key):
    """Returns localized string for the given key."""
    return translations.get(LANG, translations['en']).get(text_key, text_key)

# === Check and install dependencies ===
def check_and_install_packages():
    """Check for required libraries and install if missing."""
    print(_("checking_libraries"))
    
    # Check for OpenCV
    try:
        import cv2
        print(_("library_installed").format("cv2"))
        print(_("opencv_imported").format(cv2.__version__))
        return True
    except ImportError:
        print(_("library_not_found").format("cv2"))
        
        # Try to install opencv-python
        try:
            python_executable = sys.executable
            print(f"Python path: {python_executable}")
            
            # Install opencv-python
            result = subprocess.run(
                [python_executable, "-m", "pip", "install", "opencv-python"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print("OpenCV installed successfully")
                # Try importing again
                import cv2
                print(_("opencv_imported").format(cv2.__version__))
                return True
            else:
                print(f"Failed to install OpenCV: {result.stderr}")
                return False
                
        except Exception as e:
            print(_("failed_import_opencv").format(str(e)))
            return False

# Check dependencies
if not check_and_install_packages():
    print(_("script_cant_work"))
    input(_("press_enter"))
    sys.exit(1)

# Now we can import cv2
import cv2

# Check for PyQt5 availability
use_gui = False
try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
    print(_("pyqt5_available"))
    use_gui = True
except ImportError as e:
    print(_("pyqt5_unavailable").format(str(e)))
    use_gui = False

# === Part 2: Fisheye Projection Functions ===

class FisheyeModel:
    """Enum for fisheye projection models"""
    EQUIDISTANT = "equidistant"
    STEREOGRAPHIC = "stereographic"
    ORTHOGRAPHIC = "orthographic"
    EQUISOLID = "equisolid"

def fisheye_to_direction(x, y, img_width, img_height, focal_length, model=FisheyeModel.EQUIDISTANT, k1=0, k2=0, k3=0, k4=0):
    """
    Convert fisheye image coordinates to 3D direction vector.
    
    Parameters:
    -----------
    x, y : float
        Image coordinates
    img_width, img_height : int
        Image dimensions
    focal_length : float
        Focal length in pixels
    model : FisheyeModel
        Fisheye projection model
    k1, k2, k3, k4 : float
        Radial distortion coefficients
    
    Returns:
    --------
    numpy.array
        3D direction vector [x, y, z]
    """
    # Normalize coordinates to camera center
    cx = img_width / 2.0
    cy = img_height / 2.0
    
    x_norm = (x - cx) / focal_length
    y_norm = (y - cy) / focal_length
    
    r = np.sqrt(x_norm**2 + y_norm**2)
    
    # Apply distortion model
    if model == FisheyeModel.EQUIDISTANT:
        theta = r
    elif model == FisheyeModel.STEREOGRAPHIC:
        theta = 2 * np.arctan(r / 2)
    elif model == FisheyeModel.ORTHOGRAPHIC:
        theta = np.arcsin(np.clip(r, -1, 1))
    elif model == FisheyeModel.EQUISOLID:
        theta = 2 * np.arcsin(np.clip(r / 2, -1, 1))
    else:
        theta = r  # Default to equidistant
    
    # Apply radial distortion correction
    theta_d = theta * (1 + k1*theta**2 + k2*theta**4 + k3*theta**6 + k4*theta**8)
    
    # Convert to 3D direction
    if r > 0:
        phi = np.arctan2(y_norm, x_norm)
        direction = np.array([
            np.sin(theta_d) * np.cos(phi),
            np.sin(theta_d) * np.sin(phi),
            np.cos(theta_d)
        ])
    else:
        direction = np.array([0, 0, 1])
    
    return direction

def direction_to_perspective(direction, fov_h, fov_v, img_width, img_height):
    """
    Convert 3D direction to perspective image coordinates.
    
    Parameters:
    -----------
    direction : numpy.array
        3D direction vector
    fov_h, fov_v : float
        Horizontal and vertical field of view in radians
    img_width, img_height : int
        Output image dimensions
        
    Returns:
    --------
    tuple
        (x, y) coordinates in perspective image, or None if outside FOV
    """
    x, y, z = direction
    
    # Check if direction is behind camera
    if z <= 0:
        return None
    
    # Project to perspective
    x_proj = x / z
    y_proj = y / z
    
    # Convert to image coordinates
    fx = img_width / (2 * np.tan(fov_h / 2))
    fy = img_height / (2 * np.tan(fov_v / 2))
    
    u = x_proj * fx + img_width / 2
    v = y_proj * fy + img_height / 2
    
    # Check if within image bounds
    if 0 <= u < img_width and 0 <= v < img_height:
        return (u, v)
    else:
        return None

def create_perspective_map(fisheye_width, fisheye_height, persp_width, persp_height,
                         fisheye_focal, persp_fov, yaw, pitch, roll=0,
                         fisheye_model=FisheyeModel.EQUIDISTANT, k1=0, k2=0, k3=0, k4=0):
    """
    Create mapping from perspective image to fisheye image.
    
    Parameters:
    -----------
    fisheye_width, fisheye_height : int
        Fisheye image dimensions
    persp_width, persp_height : int
        Perspective image dimensions
    fisheye_focal : float
        Fisheye focal length in pixels
    persp_fov : float
        Perspective field of view in degrees
    yaw, pitch, roll : float
        Camera rotation angles in degrees
    fisheye_model : FisheyeModel
        Fisheye projection model
    k1, k2, k3, k4 : float
        Distortion coefficients
        
    Returns:
    --------
    tuple
        (map_x, map_y) for cv2.remap(), and valid_mask
    """
    print(_("creating_projection_map"))
    
    # Convert angles to radians
    yaw_rad = np.radians(yaw)
    pitch_rad = np.radians(pitch)
    roll_rad = np.radians(roll)
    fov_rad = np.radians(persp_fov)
    
    # Create rotation matrix
    # Roll (around Z axis)
    R_roll = np.array([
        [np.cos(roll_rad), -np.sin(roll_rad), 0],
        [np.sin(roll_rad), np.cos(roll_rad), 0],
        [0, 0, 1]
    ])
    
    # Pitch (around X axis)
    R_pitch = np.array([
        [1, 0, 0],
        [0, np.cos(pitch_rad), -np.sin(pitch_rad)],
        [0, np.sin(pitch_rad), np.cos(pitch_rad)]
    ])
    
    # Yaw (around Y axis)
    R_yaw = np.array([
        [np.cos(yaw_rad), 0, np.sin(yaw_rad)],
        [0, 1, 0],
        [-np.sin(yaw_rad), 0, np.cos(yaw_rad)]
    ])
    
    # Combined rotation matrix
    R = R_yaw @ R_pitch @ R_roll
    
    # Initialize maps
    map_x = np.zeros((persp_height, persp_width), dtype=np.float32)
    map_y = np.zeros((persp_height, persp_width), dtype=np.float32)
    valid_mask = np.zeros((persp_height, persp_width), dtype=np.uint8)
    
    # Perspective camera parameters
    fx = persp_width / (2 * np.tan(fov_rad / 2))
    fy = fx  # Assuming square pixels
    cx = persp_width / 2
    cy = persp_height / 2
    
    # For each pixel in perspective image
    for v in range(persp_height):
        for u in range(persp_width):
            # Convert to normalized camera coordinates
            x = (u - cx) / fx
            y = (v - cy) / fy
            z = 1.0
            
            # Create direction vector and rotate
            direction = np.array([x, y, z])
            direction = direction / np.linalg.norm(direction)
            direction_rotated = R @ direction
            
            # Convert direction to fisheye coordinates
            if direction_rotated[2] > 0:  # Check if in front of camera
                # Calculate theta and phi
                theta = np.arccos(direction_rotated[2])
                phi = np.arctan2(direction_rotated[1], direction_rotated[0])
                
                # Apply fisheye model (inverse)
                if fisheye_model == FisheyeModel.EQUIDISTANT:
                    r = theta
                elif fisheye_model == FisheyeModel.STEREOGRAPHIC:
                    r = 2 * np.tan(theta / 2)
                elif fisheye_model == FisheyeModel.ORTHOGRAPHIC:
                    r = np.sin(theta)
                elif fisheye_model == FisheyeModel.EQUISOLID:
                    r = 2 * np.sin(theta / 2)
                else:
                    r = theta
                
                # Apply distortion (simplified - would need iterative solution for full accuracy)
                r_distorted = r * (1 + k1*r**2 + k2*r**4)
                
                # Convert to fisheye image coordinates
                x_fisheye = r_distorted * np.cos(phi) * fisheye_focal + fisheye_width / 2
                y_fisheye = r_distorted * np.sin(phi) * fisheye_focal + fisheye_height / 2
                
                # Check if within fisheye image bounds
                if 0 <= x_fisheye < fisheye_width and 0 <= y_fisheye < fisheye_height:
                    map_x[v, u] = x_fisheye
                    map_y[v, u] = y_fisheye
                    valid_mask[v, u] = 255
    
    return map_x, map_y, valid_mask

def generate_perspective_views(num_views, arrangement="horizontal"):
    """
    Generate yaw and pitch angles for multiple perspective views.
    
    Parameters:
    -----------
    num_views : int
        Number of perspective views to generate
    arrangement : str
        Arrangement pattern: "horizontal", "vertical", "grid", "custom"
        
    Returns:
    --------
    list
        List of (yaw, pitch) tuples in degrees
    """
    views = []
    
    if arrangement == "horizontal":
        # Arrange views horizontally around the center
        if num_views == 1:
            views = [(0, 0)]
        else:
            step = 360 / num_views
            for i in range(num_views):
                yaw = i * step
                views.append((yaw, 0))
                
    elif arrangement == "vertical":
        # Arrange views vertically
        if num_views == 1:
            views = [(0, 0)]
        elif num_views == 2:
            views = [(0, 45), (0, -45)]
        else:
            pitch_range = 90
            step = pitch_range / (num_views - 1)
            for i in range(num_views):
                pitch = pitch_range/2 - i * step
                views.append((0, pitch))
                
    elif arrangement == "grid":
        # Arrange in a grid pattern
        if num_views <= 4:
            # 2x2 grid
            views = [(0, 0), (90, 0), (180, 0), (270, 0)][:num_views]
        elif num_views <= 6:
            # Cubic arrangement
            views = [(0, 0), (90, 0), (180, 0), (270, 0), (0, 90), (0, -90)][:num_views]
        else:
            # More complex grid
            rows = int(np.sqrt(num_views))
            cols = (num_views + rows - 1) // rows
            yaw_step = 360 / cols
            pitch_step = 180 / (rows + 1)
            
            for r in range(rows):
                for c in range(cols):
                    if len(views) < num_views:
                        yaw = c * yaw_step
                        pitch = 90 - (r + 1) * pitch_step
                        views.append((yaw, pitch))
                        
    print(_("views_arranged").format(arrangement))
    return views

# === Part 3: Image Processing Functions ===

def convert_fisheye_to_perspective(fisheye_image_path, output_folder, camera_label,
                                 perspective_params, fisheye_focal, fisheye_model,
                                 output_size, overlap=10, file_format="jpg", quality=95,
                                 interpolation=cv2.INTER_CUBIC, k1=0, k2=0, k3=0, k4=0,
                                 generate_mask=True):
    """
    Convert a fisheye image to multiple perspective views.
    
    Parameters:
    -----------
    fisheye_image_path : str
        Path to fisheye image
    output_folder : str
        Output directory
    camera_label : str
        Camera label for naming
    perspective_params : list
        List of (yaw, pitch) tuples for each view
    fisheye_focal : float
        Fisheye focal length in pixels
    fisheye_model : FisheyeModel
        Fisheye projection model
    output_size : int
        Output image size (width and height)
    overlap : float
        Overlap in degrees
    file_format : str
        Output format (jpg, png, tiff)
    quality : int
        JPEG quality (1-100)
    interpolation : int
        OpenCV interpolation method
    k1, k2, k3, k4 : float
        Distortion coefficients
    generate_mask : bool
        Whether to generate masks for valid areas
        
    Returns:
    --------
    dict
        Dictionary mapping view names to file paths
    """
    # Load fisheye image
    fisheye_image = cv2.imread(fisheye_image_path)
    if fisheye_image is None:
        raise ValueError(f"Failed to load image: {fisheye_image_path}")
    
    fisheye_height, fisheye_width = fisheye_image.shape[:2]
    
    # Create output directory
    os.makedirs(output_folder, exist_ok=True)
    
    # Setup save parameters
    if file_format.lower() in ["jpg", "jpeg"]:
        save_params = [cv2.IMWRITE_JPEG_QUALITY, quality]
        ext = "jpg"
    elif file_format.lower() == "png":
        save_params = [cv2.IMWRITE_PNG_COMPRESSION, min(9, 10 - quality//10)]
        ext = "png"
    else:
        save_params = []
        ext = file_format.lower()
    
    output_paths = {}
    
    # Process each perspective view
    for i, (yaw, pitch) in enumerate(perspective_params):
        view_name = f"persp_{i:02d}"
        print(_("creating_perspective_view").format(view_name))
        print(_("perspective_params").format(view_name, yaw, pitch))
        
        # Create projection maps
        persp_fov = 90 + overlap  # Add overlap
        map_x, map_y, valid_mask = create_perspective_map(
            fisheye_width, fisheye_height,
            output_size, output_size,
            fisheye_focal, persp_fov,
            yaw, pitch, 0,
            fisheye_model, k1, k2, k3, k4
        )
        
        # Apply remapping
        print(_("applying_remap"))
        perspective_image = cv2.remap(fisheye_image, map_x, map_y, interpolation,
                                    borderMode=cv2.BORDER_CONSTANT, borderValue=(0, 0, 0))
        
        # Save perspective image
        output_filename = f"{camera_label}_{view_name}.{ext}"
        output_path = os.path.join(output_folder, output_filename)
        
        print(_("saving_image").format(output_filename))
        if not cv2.imwrite(output_path, perspective_image, save_params):
            print(_("save_error").format(output_path))
            continue
            
        output_paths[view_name] = output_path
        
        # Generate and save mask if requested
        if generate_mask:
            mask_filename = f"{camera_label}_{view_name}_mask.png"
            mask_path = os.path.join(output_folder, mask_filename)
            cv2.imwrite(mask_path, valid_mask)
            output_paths[f"{view_name}_mask"] = mask_path
            print(_("mask_generated").format(mask_filename))
    
    print(_("conversion_complete").format(camera_label))
    return output_paths

# === Part 4: Metashape Integration Functions ===

def detect_fisheye_cameras(chunk):
    """
    Detect fisheye cameras in the chunk based on sensor type or camera model.
    
    Parameters:
    -----------
    chunk : Metashape.Chunk
        The chunk to search for cameras
        
    Returns:
    --------
    list
        List of fisheye cameras
    """
    fisheye_cameras = []
    
    for camera in chunk.cameras:
        if camera.sensor and camera.transform and camera.photo:
            # Check if sensor type is fisheye
            if camera.sensor.type == Metashape.Sensor.Type.Fisheye:
                fisheye_cameras.append(camera)
            # Also check camera label for common fisheye indicators
            elif any(keyword in camera.label.lower() for keyword in ['fisheye', 'fish-eye', 'omnidirectional']):
                fisheye_cameras.append(camera)
    
    return fisheye_cameras

def get_fisheye_calibration(camera):
    """
    Extract fisheye calibration parameters from Metashape camera.
    
    Parameters:
    -----------
    camera : Metashape.Camera
        The fisheye camera
        
    Returns:
    --------
    dict
        Calibration parameters
    """
    calibration = {}
    
    if camera.sensor and camera.sensor.calibration:
        cal = camera.sensor.calibration
        calibration['focal_length'] = cal.f if cal.f else camera.sensor.focal_length
        calibration['cx'] = cal.cx if cal.cx else camera.sensor.width / 2
        calibration['cy'] = cal.cy if cal.cy else camera.sensor.height / 2
        calibration['k1'] = cal.k1 if hasattr(cal, 'k1') else 0
        calibration['k2'] = cal.k2 if hasattr(cal, 'k2') else 0
        calibration['k3'] = cal.k3 if hasattr(cal, 'k3') else 0
        calibration['k4'] = cal.k4 if hasattr(cal, 'k4') else 0
        
        # Try to detect fisheye model from metadata or sensor info
        if hasattr(camera.sensor, 'fisheye_affinity'):
            # Metashape specific fisheye parameters
            calibration['model'] = FisheyeModel.EQUIDISTANT  # Default
        else:
            calibration['model'] = FisheyeModel.EQUIDISTANT
            
        print(_("calibration_detected").format(calibration['focal_length'], calibration['k1']))
    else:
        # Default calibration
        print(_("no_calibration"))
        calibration = {
            'focal_length': camera.sensor.focal_length if camera.sensor else 1000,
            'cx': camera.sensor.width / 2 if camera.sensor else 1000,
            'cy': camera.sensor.height / 2 if camera.sensor else 1000,
            'k1': 0, 'k2': 0, 'k3': 0, 'k4': 0,
            'model': FisheyeModel.EQUIDISTANT
        }
    
    return calibration

def add_perspective_cameras(chunk, fisheye_camera, image_paths, output_size, persp_fov, view_params):
    """
    Add perspective cameras to Metashape chunk.
    
    Parameters:
    -----------
    chunk : Metashape.Chunk
        The chunk to add cameras to
    fisheye_camera : Metashape.Camera
        Original fisheye camera
    image_paths : dict
        Paths to perspective images
    output_size : int
        Size of perspective images
    persp_fov : float
        Field of view in degrees
    view_params : list
        List of (yaw, pitch) for each view
        
    Returns:
    --------
    list
        List of created cameras
    """
    print(_("adding_perspective_cameras"))
    
    # Get position from fisheye camera
    position = fisheye_camera.transform.translation()
    base_rotation = fisheye_camera.transform.rotation()
    
    cameras_created = []
    
    for i, (view_name, image_path) in enumerate(image_paths.items()):
        if "_mask" in view_name:
            continue  # Skip mask files
            
        # Create new camera
        camera = chunk.addCamera()
        if camera is None:
            print(f"Failed to add camera for {view_name}")
            continue
            
        camera.label = f"{fisheye_camera.label}_{view_name}"
        print(_("camera_added").format(camera.label))
        
        # Find or create sensor
        persp_sensors = [s for s in chunk.sensors 
                        if s.type == Metashape.Sensor.Type.Frame 
                        and s.width == output_size 
                        and s.height == output_size]
        
        if persp_sensors:
            camera.sensor = persp_sensors[0]
        else:
            # Create new sensor
            sensor = chunk.addSensor()
            sensor.label = f"Perspective_{output_size}px"
            sensor.type = Metashape.Sensor.Type.Frame
            sensor.width = output_size
            sensor.height = output_size
            sensor.focal_length = output_size / (2 * np.tan(np.radians(persp_fov / 2)))
            camera.sensor = sensor
            print(_("sensor_created").format(sensor.label))
        
        # Set calibration
        calibration = camera.sensor.calibration
        calibration.f = camera.sensor.focal_length
        calibration.cx = output_size / 2
        calibration.cy = output_size / 2
        calibration.k1 = 0
        calibration.k2 = 0
        calibration.k3 = 0
        calibration.p1 = 0
        calibration.p2 = 0
        print(_("calibration_set").format(camera.label))
        
        # Set position (same as fisheye)
        camera.transform = Metashape.Matrix.Translation(position)
        
        # Calculate and apply rotation
        view_idx = int(view_name.split('_')[1])
        yaw, pitch = view_params[view_idx]
        
        # Create rotation matrix for the view
        yaw_rad = np.radians(yaw)
        pitch_rad = np.radians(pitch)
        
        # Rotation matrices
        R_yaw = Metashape.Matrix([
            [np.cos(yaw_rad), 0, np.sin(yaw_rad)],
            [0, 1, 0],
            [-np.sin(yaw_rad), 0, np.cos(yaw_rad)]
        ])
        
        R_pitch = Metashape.Matrix([
            [1, 0, 0],
            [0, np.cos(pitch_rad), -np.sin(pitch_rad)],
            [0, np.sin(pitch_rad), np.cos(pitch_rad)]
        ])
        
        # Apply rotations
        rotation = base_rotation * R_yaw * R_pitch
        transform_4x4 = Metashape.Matrix([
            [rotation[0,0], rotation[0,1], rotation[0,2], position.x],
            [rotation[1,0], rotation[1,1], rotation[1,2], position.y],
            [rotation[2,0], rotation[2,1], rotation[2,2], position.z],
            [0, 0, 0, 1]
        ])
        camera.transform = transform_4x4
        print(_("transform_set").format(camera.label))
        
        # Set photo
        camera.photo = Metashape.Photo()
        camera.photo.path = image_path
        
        # Set metadata
        camera.meta['Image/Width'] = str(output_size)
        camera.meta['Image/Height'] = str(output_size)
        camera.meta['Image/Model'] = "Perspective from Fisheye"
        print(_("metadata_updated").format(camera.label))
        
        cameras_created.append(camera)
    
    return cameras_created

def realign_cameras(chunk):
    """Realign all cameras in the chunk."""
    print(_("realigning_cameras"))
    try:
        chunk.matchPhotos(
            generic_preselection=True,
            reference_preselection=False,
            filter_mask=False,
            keypoint_limit=40000,
            tiepoint_limit=4000,
            guided_matching=False,
            reset_matches=True
        )
        
        chunk.alignCameras(
            adaptive_fitting=False,
            reset_alignment=True
        )
        
        print(_("alignment_complete"))
        return True
    except Exception as e:
        print(f"Alignment error: {str(e)}")
        return False

def remove_fisheye_cameras(chunk, fisheye_cameras):
    """Remove original fisheye cameras from the chunk."""
    print(_("removing_fisheye_cameras"))
    try:
        for camera in fisheye_cameras:
            chunk.remove(camera)
        print(_("removal_complete"))
        return True
    except Exception as e:
        print(f"Removal error: {str(e)}")
        return False

# === Part 5: GUI Implementation ===

if use_gui:
    class ProcessingThread(QThread):
        """Thread for processing fisheye cameras without blocking GUI."""
        
        update_progress = pyqtSignal(int, int, str, str, int)  # current, total, camera_name, status, percent
        processing_finished = pyqtSignal(bool, dict)  # success, stats
        error_occurred = pyqtSignal(str)  # error message
        
        def __init__(self, cameras, output_folder, options):
            super().__init__()
            self.cameras = cameras
            self.output_folder = output_folder
            self.options = options
            self.stop_requested = False
            
        def run(self):
            try:
                start_time = time.time()
                total_cameras = len(self.cameras)
                processed_count = 0
                errors = []
                
                for i, camera in enumerate(self.cameras):
                    if self.stop_requested:
                        break
                        
                    camera_label = camera.label
                    self.update_progress.emit(
                        i + 1, total_cameras, camera_label,
                        _("processing_status"), 
                        int((i + 1) / total_cameras * 100)
                    )
                    
                    try:
                        # Get calibration
                        calibration = get_fisheye_calibration(camera)
                        
                        # Generate view parameters
                        view_params = generate_perspective_views(
                            self.options['num_views'],
                            self.options['arrangement']
                        )
                        
                        # Convert images
                        image_paths = convert_fisheye_to_perspective(
                            fisheye_image_path=camera.photo.path,
                            output_folder=self.output_folder,
                            camera_label=camera_label,
                            perspective_params=view_params,
                            fisheye_focal=calibration['focal_length'],
                            fisheye_model=self.options.get('fisheye_model', calibration['model']),
                            output_size=self.options['output_size'],
                            overlap=self.options['overlap'],
                            file_format=self.options['file_format'],
                            quality=self.options['quality'],
                            interpolation=self.options['interpolation'],
                            k1=calibration['k1'],
                            k2=calibration['k2'],
                            k3=calibration['k3'],
                            k4=calibration['k4'],
                            generate_mask=self.options['generate_masks']
                        )
                        
                        # Add cameras to Metashape
                        if image_paths:
                            chunk = Metashape.app.document.chunk
                            add_perspective_cameras(
                                chunk, camera, image_paths,
                                self.options['output_size'],
                                self.options['persp_fov'],
                                view_params
                            )
                            processed_count += 1
                            
                    except Exception as e:
                        error_msg = _("processing_error").format(camera_label, str(e))
                        print(error_msg)
                        print(traceback.format_exc())
                        errors.append(error_msg)
                
                # Post-processing
                if not self.stop_requested:
                    chunk = Metashape.app.document.chunk
                    
                    if self.options.get('realign_cameras', False):
                        self.update_progress.emit(
                            total_cameras, total_cameras, "",
                            _("realigning_cameras"), 90
                        )
                        realign_cameras(chunk)
                        
                    if self.options.get('remove_fisheye', False):
                        self.update_progress.emit(
                            total_cameras, total_cameras, "",
                            _("removing_fisheye_cameras"), 95
                        )
                        remove_fisheye_cameras(chunk, self.cameras)
                
                # Finish
                total_time = time.time() - start_time
                self.processing_finished.emit(True, {
                    'processed': processed_count,
                    'total': total_cameras,
                    'errors': errors,
                    'time': total_time
                })
                
            except Exception as e:
                self.error_occurred.emit(str(e))
                
        def stop(self):
            self.stop_requested = True

    class FisheyeConverterGUI(QMainWindow):
        """Main GUI window for fisheye conversion."""
        
        def __init__(self):
            super().__init__()
            self.process_thread = None
            self.init_ui()
            self.load_cameras()
            
        def init_ui(self):
            self.setWindowTitle(_("script_title"))
            self.setMinimumSize(600, 700)
            
            # Central widget
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)
            
            # Settings group
            settings_group = QGroupBox(_("settings_group"))
            settings_layout = QGridLayout()
            
            # Output folder
            settings_layout.addWidget(QLabel(_("output_folder_label")), 0, 0)
            self.output_folder_path = QLineEdit(_("not_selected"))
            self.output_folder_path.setReadOnly(True)
            settings_layout.addWidget(self.output_folder_path, 0, 1)
            self.browse_button = QPushButton(_("browse_button"))
            self.browse_button.clicked.connect(self.select_output_folder)
            settings_layout.addWidget(self.browse_button, 0, 2)
            
            # Fisheye model
            settings_layout.addWidget(QLabel(_("fisheye_model_label")), 1, 0)
            self.model_combo = QComboBox()
            self.model_combo.addItems([
                _("equidistant"),
                _("stereographic"),
                _("orthographic"),
                _("equisolid")
            ])
            settings_layout.addWidget(self.model_combo, 1, 1, 1, 2)
            
            # Number of views
            settings_layout.addWidget(QLabel(_("perspective_count_label")), 2, 0)
            self.views_spinner = QSpinBox()
            self.views_spinner.setRange(1, 12)
            self.views_spinner.setValue(4)
            settings_layout.addWidget(self.views_spinner, 2, 1, 1, 2)
            
            # View arrangement
            settings_layout.addWidget(QLabel(_("arrangement_label")), 3, 0)
            self.arrangement_combo = QComboBox()
            self.arrangement_combo.addItems([
                _("horizontal"),
                _("vertical"),
                _("grid")
            ])
            settings_layout.addWidget(self.arrangement_combo, 3, 1, 1, 2)
            
            # FOV
            settings_layout.addWidget(QLabel(_("perspective_fov_label")), 4, 0)
            self.fov_spinner = QSpinBox()
            self.fov_spinner.setRange(10, 120)
            self.fov_spinner.setValue(90)
            self.fov_spinner.setSuffix("°")
            settings_layout.addWidget(self.fov_spinner, 4, 1, 1, 2)
            
            # Overlap
            settings_layout.addWidget(QLabel(_("overlap_label")), 5, 0)
            self.overlap_spinner = QSpinBox()
            self.overlap_spinner.setRange(0, 30)
            self.overlap_spinner.setValue(10)
            self.overlap_spinner.setSuffix("°")
            settings_layout.addWidget(self.overlap_spinner, 5, 1, 1, 2)
            
            # Processing threads
            settings_layout.addWidget(QLabel(_("threads_label")), 6, 0)
            self.threads_spinner = QSpinBox()
            self.threads_spinner.setRange(1, os.cpu_count() or 1)
            self.threads_spinner.setValue(min(4, os.cpu_count() or 1))
            settings_layout.addWidget(self.threads_spinner, 6, 1, 1, 2)
            
            settings_group.setLayout(settings_layout)
            layout.addWidget(settings_group)
            
            # Image parameters group
            image_group = QGroupBox(_("image_group"))
            image_layout = QGridLayout()
            
            # Output size
            image_layout.addWidget(QLabel(_("image_size_label")), 0, 0)
            self.size_combo = QComboBox()
            self.size_combo.addItems(["512", "1024", "2048", "4096"])
            self.size_combo.setCurrentIndex(1)
            image_layout.addWidget(self.size_combo, 0, 1)
            
            # File format
            image_layout.addWidget(QLabel(_("file_format_label")), 1, 0)
            self.format_combo = QComboBox()
            self.format_combo.addItems(["JPEG", "PNG", "TIFF"])
            image_layout.addWidget(self.format_combo, 1, 1)
            
            # Quality
            image_layout.addWidget(QLabel(_("quality_label")), 2, 0)
            self.quality_spinner = QSpinBox()
            self.quality_spinner.setRange(1, 100)
            self.quality_spinner.setValue(95)
            image_layout.addWidget(self.quality_spinner, 2, 1)
            
            # Interpolation
            image_layout.addWidget(QLabel(_("interpolation_label")), 3, 0)
            self.interp_combo = QComboBox()
            self.interp_combo.addItem(_("nearest"), cv2.INTER_NEAREST)
            self.interp_combo.addItem(_("linear"), cv2.INTER_LINEAR)
            self.interp_combo.addItem(_("cubic"), cv2.INTER_CUBIC)
            self.interp_combo.setCurrentIndex(2)
            image_layout.addWidget(self.interp_combo, 3, 1)
            
            image_group.setLayout(image_layout)
            layout.addWidget(image_group)
            
            # Post-processing group
            post_group = QGroupBox(_("post_processing_group"))
            post_layout = QVBoxLayout()
            
            self.generate_masks_check = QCheckBox(_("generate_masks"))
            self.generate_masks_check.setChecked(True)
            post_layout.addWidget(self.generate_masks_check)
            
            self.realign_check = QCheckBox(_("realign_cameras"))
            post_layout.addWidget(self.realign_check)
            
            self.remove_fisheye_check = QCheckBox(_("remove_fisheye"))
            post_layout.addWidget(self.remove_fisheye_check)
            
            post_group.setLayout(post_layout)
            layout.addWidget(post_group)
            
            # Project info group
            info_group = QGroupBox(_("project_info_group"))
            info_layout = QHBoxLayout()
            
            info_layout.addWidget(QLabel(_("cameras_count_label")))
            self.cameras_count_label = QLabel("0")
            info_layout.addWidget(self.cameras_count_label)
            info_layout.addStretch()
            
            info_group.setLayout(info_layout)
            layout.addWidget(info_group)
            
            # Progress group
            progress_group = QGroupBox(_("progress_group"))
            progress_layout = QGridLayout()
            
            progress_layout.addWidget(QLabel(_("current_camera_label")), 0, 0)
            self.current_camera_label = QLabel(_("no_camera"))
            progress_layout.addWidget(self.current_camera_label, 0, 1)
            
            progress_layout.addWidget(QLabel(_("status_label")), 1, 0)
            self.status_label = QLabel(_("waiting_start"))
            progress_layout.addWidget(self.status_label, 1, 1)
            
            progress_layout.addWidget(QLabel(_("time_remaining_label")), 2, 0)
            self.time_label = QLabel(_("time_format"))
            progress_layout.addWidget(self.time_label, 2, 1)
            
            self.progress_bar = QProgressBar()
            progress_layout.addWidget(self.progress_bar, 3, 0, 1, 2)
            
            progress_group.setLayout(progress_layout)
            layout.addWidget(progress_group)
            
            # Buttons
            button_layout = QHBoxLayout()
            
            self.start_button = QPushButton(_("start_button"))
            self.start_button.clicked.connect(self.start_processing)
            button_layout.addWidget(self.start_button)
            
            self.stop_button = QPushButton(_("stop_button"))
            self.stop_button.clicked.connect(self.stop_processing)
            self.stop_button.setEnabled(False)
            button_layout.addWidget(self.stop_button)
            
            self.close_button = QPushButton(_("close_button"))
            self.close_button.clicked.connect(self.close)
            button_layout.addWidget(self.close_button)
            
            layout.addLayout(button_layout)
            
            # Status bar
            self.status_bar = self.statusBar()
            self.status_bar.showMessage(_("waiting_start"))
            
        def load_cameras(self):
            """Load fisheye cameras from the current project."""
            try:
                doc = Metashape.app.document
                if doc and doc.chunk:
                    fisheye_cameras = detect_fisheye_cameras(doc.chunk)
                    self.cameras_count_label.setText(str(len(fisheye_cameras)))
                    
                    if not fisheye_cameras:
                        QMessageBox.warning(self, _("warning_title"), _("no_fisheye_cameras"))
                else:
                    QMessageBox.warning(self, _("error_title"), _("no_chunk_error"))
            except Exception as e:
                QMessageBox.critical(self, _("error_title"), str(e))
                
        def select_output_folder(self):
            """Select output folder for converted images."""
            folder = QFileDialog.getExistingDirectory(self, _("select_output_folder"))
            if folder:
                self.output_folder_path.setText(folder)
                
        def start_processing(self):
            """Start the conversion process."""
            # Validate inputs
            if self.output_folder_path.text() == _("not_selected"):
                QMessageBox.warning(self, _("warning_title"), _("no_output_folder"))
                return
                
            # Get cameras
            doc = Metashape.app.document
            if not doc or not doc.chunk:
                QMessageBox.warning(self, _("error_title"), _("no_chunk_error"))
                return
                
            fisheye_cameras = detect_fisheye_cameras(doc.chunk)
            if not fisheye_cameras:
                QMessageBox.warning(self, _("warning_title"), _("no_fisheye_cameras"))
                return
                
            # Prepare options
            options = {
                'fisheye_model': [
                    FisheyeModel.EQUIDISTANT,
                    FisheyeModel.STEREOGRAPHIC,
                    FisheyeModel.ORTHOGRAPHIC,
                    FisheyeModel.EQUISOLID
                ][self.model_combo.currentIndex()],
                'num_views': self.views_spinner.value(),
                'arrangement': ['horizontal', 'vertical', 'grid'][self.arrangement_combo.currentIndex()],
                'persp_fov': self.fov_spinner.value(),
                'overlap': self.overlap_spinner.value(),
                'output_size': int(self.size_combo.currentText()),
                'file_format': ['jpg', 'png', 'tiff'][self.format_combo.currentIndex()],
                'quality': self.quality_spinner.value(),
                'interpolation': self.interp_combo.currentData(),
                'generate_masks': self.generate_masks_check.isChecked(),
                'realign_cameras': self.realign_check.isChecked(),
                'remove_fisheye': self.remove_fisheye_check.isChecked(),
                'threads': self.threads_spinner.value()
            }
            
            # Create and start processing thread
            self.process_thread = ProcessingThread(
                fisheye_cameras,
                self.output_folder_path.text(),
                options
            )
            
            self.process_thread.update_progress.connect(self.update_progress)
            self.process_thread.processing_finished.connect(self.processing_finished)
            self.process_thread.error_occurred.connect(self.processing_error)
            
            # Update UI
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.progress_bar.setValue(0)
            self.status_label.setText(_("processing_status"))
            
            # Start processing
            self.process_thread.start()
            
            # Start timer for time remaining
            self.start_time = time.time()
            self.timer = QTimer()
            self.timer.timeout.connect(self.update_time_remaining)
            self.timer.start(1000)
            
        def stop_processing(self):
            """Stop the conversion process."""
            if self.process_thread and self.process_thread.isRunning():
                reply = QMessageBox.question(
                    self, _("warning_title"),
                    _("processing_aborted"),
                    QMessageBox.Yes | QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    self.process_thread.stop()
                    self.status_label.setText(_("aborted_status"))
                    
        def update_progress(self, current, total, camera_name, status, percent):
            """Update progress display."""
            self.progress_bar.setValue(percent)
            self.current_camera_label.setText(camera_name)
            self.status_label.setText(status)
            self.status_bar.showMessage(f"{current}/{total} - {status}")
            
        def update_time_remaining(self):
            """Update estimated time remaining."""
            if self.process_thread and self.process_thread.isRunning():
                elapsed = time.time() - self.start_time
                progress = self.progress_bar.value() / 100.0
                
                if progress > 0:
                    total_time = elapsed / progress
                    remaining = total_time - elapsed
                    
                    hours = int(remaining // 3600)
                    minutes = int((remaining % 3600) // 60)
                    seconds = int(remaining % 60)
                    
                    self.time_label.setText(f"{hours:02d}:{minutes:02d}:{seconds:02d}")
                    
        def processing_finished(self, success, stats):
            """Handle processing completion."""
            if hasattr(self, 'timer'):
                self.timer.stop()
                
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            
            if success:
                message = _("processing_finished") + "\n\n"
                message += f"Processed: {stats['processed']}/{stats['total']}\n"
                message += _("total_time").format(f"{stats['time']:.1f}s")
                
                if stats['errors']:
                    message += f"\n\nErrors: {len(stats['errors'])}"
                    
                QMessageBox.information(self, _("info_title"), message)
                self.status_label.setText(_("completed_status"))
            else:
                QMessageBox.warning(self, _("warning_title"), _("processing_aborted"))
                self.status_label.setText(_("aborted_status"))
                
        def processing_error(self, error_message):
            """Handle processing errors."""
            QMessageBox.critical(self, _("error_title"), error_message)
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText(_("error_status"))

# === Part 6: Console Mode Functions ===

def process_console():
    """Console mode processing."""
    print(_("console_mode"))
    
    try:
        # Check for active document and chunk
        doc = Metashape.app.document
        if not doc or not doc.chunk:
            print(_("no_chunk_error"))
            return
            
        chunk = doc.chunk
        
        # Detect fisheye cameras
        fisheye_cameras = detect_fisheye_cameras(chunk)
        if not fisheye_cameras:
            print(_("no_fisheye_cameras"))
            return
            
        print(_("found_cameras").format(len(fisheye_cameras)))
        
        # Get output folder
        output_folder = Metashape.app.getExistingDirectory(_("select_output_folder"))
        if not output_folder:
            return
            
        # Get conversion parameters
        model_options = [_("equidistant"), _("stereographic"), _("orthographic"), _("equisolid")]
        model_idx = Metashape.app.getInt(_("console_select_model"), model_options)
        fisheye_model = [
            FisheyeModel.EQUIDISTANT,
            FisheyeModel.STEREOGRAPHIC,
            FisheyeModel.ORTHOGRAPHIC,
            FisheyeModel.EQUISOLID
        ][model_idx] if model_idx is not None else FisheyeModel.EQUIDISTANT
        
        num_views = Metashape.app.getInt(_("console_select_views"), 4)
        if not num_views or num_views < 1:
            num_views = 4
            
        persp_fov = Metashape.app.getFloat(_("console_select_fov"), 90.0)
        if not persp_fov:
            persp_fov = 90.0
            
        overlap = Metashape.app.getFloat(_("console_select_overlap"), 10.0)
        if overlap is None:
            overlap = 10.0
            
        size_options = ["512", "1024", "2048", "4096"]
        size_idx = Metashape.app.getInt(_("console_select_size"), size_options)
        output_size = int(size_options[size_idx]) if size_idx is not None else 1024
        
        # Post-processing options
        generate_masks = Metashape.app.getBool(_("generate_masks"))
        realign = Metashape.app.getBool(_("realign_cameras"))
        remove_fisheye = Metashape.app.getBool(_("remove_fisheye"))
        
        # Start processing
        print(_("console_processing").format(len(fisheye_cameras)))
        
        start_time = time.time()
        processed = 0
        
        # Generate view parameters
        view_params = generate_perspective_views(num_views, "horizontal")
        
        for i, camera in enumerate(fisheye_cameras):
            print(_("batch_progress").format(i + 1, len(fisheye_cameras)))
            
            try:
                # Get calibration
                calibration = get_fisheye_calibration(camera)
                
                # Convert images
                image_paths = convert_fisheye_to_perspective(
                    fisheye_image_path=camera.photo.path,
                    output_folder=output_folder,
                    camera_label=camera.label,
                    perspective_params=view_params,
                    fisheye_focal=calibration['focal_length'],
                    fisheye_model=fisheye_model,
                    output_size=output_size,
                    overlap=overlap,
                    file_format="jpg",
                    quality=95,
                    interpolation=cv2.INTER_CUBIC,
                    k1=calibration['k1'],
                    k2=calibration['k2'],
                    k3=calibration['k3'],
                    k4=calibration['k4'],
                    generate_mask=generate_masks
                )
                
                # Add cameras
                if image_paths:
                    add_perspective_cameras(
                        chunk, camera, image_paths,
                        output_size, persp_fov, view_params
                    )
                    processed += 1
                    
            except Exception as e:
                print(_("processing_error").format(camera.label, str(e)))
                
        # Post-processing
        if realign:
            realign_cameras(chunk)
            
        if remove_fisheye:
            remove_fisheye_cameras(chunk, fisheye_cameras)
            
        # Report results
        total_time = time.time() - start_time
        print(_("processing_complete"))
        print(_("total_time").format(f"{total_time:.1f}s"))
        print(f"Processed: {processed}/{len(fisheye_cameras)}")
        
    except Exception as e:
        print(_("error_title") + ": " + str(e))
        print(traceback.format_exc())

# === Part 7: Main Function ===

def main():
    """Main entry point for the script."""
    global LANG, gui_window
    
    # Simple language detection
    try:
        if Metashape and Metashape.app:
            # Check Metashape language setting
            # For now, default to English
            LANG = 'en'
    except:
        LANG = 'en'
    
    print(f"Language: {LANG}")
    print(_("script_title"))
    print(_("version") + " 1.0")
    
    # Check if GUI is available
    if use_gui:
        try:
            print(_("gui_mode"))
            print(_("init_gui"))
            
            # Initialize QApplication
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
                
            # Create and show window
            gui_window = FisheyeConverterGUI()
            gui_window.show()
            
            print(_("gui_launched"))
            
            # Don't block Metashape
            # The window will stay open and handle events
            
        except Exception as e:
            print(f"GUI error: {str(e)}")
            print(traceback.format_exc())
            process_console()
    else:
        # Run in console mode
        process_console()

# Run the script
if __name__ == "__main__":
    main()