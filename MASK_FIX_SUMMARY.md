# Mask Processing Fix Summary

## Problem
The mask processing was failing because the script was trying to use incorrect methods to save masks:
- Trying to call `mask.save()` directly (doesn't exist)
- Trying to use `chunk.exportMasks()` (not available in Metashape 2.x)

## Solution
Based on the test script output, we discovered that:
1. `Metashape.Mask` objects have an `image()` method
2. This method returns a `Metashape.Image` object
3. The `Metashape.Image` object has a `save()` method

## Code Changes
Updated the mask saving logic in `fisheye_to_pinhole_unified_improved.py`:
```python
# OLD (incorrect):
if isinstance(mask_obj, Metashape.Image):
    mask_obj.save(temp_mask_path)
elif hasattr(mask_obj, 'image'):
    mask_image = mask_obj.image()
    if mask_image and hasattr(mask_image, 'save'):
        mask_image.save(temp_mask_path)

# NEW (correct):
if hasattr(mask_obj, 'image'):
    mask_image = mask_obj.image()
    if mask_image:
        mask_image.save(temp_mask_path)
```

## Expected Behavior
With this fix, the script should now:
1. Successfully extract masks from Metashape cameras
2. Save them to temporary files
3. Apply the same fisheye-to-pinhole transformation to the masks
4. Save the transformed masks to the output folder

## Testing
Run the main script with mask processing enabled to verify:
- All cameras with masks should process successfully
- Transformed masks should appear in `output_folder/masks/`
- No more "object has no attribute 'save'" errors