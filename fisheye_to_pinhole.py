# Fisheye to Pinhole Conversion Script for Metashape
# Converts fisheye images to multiple pinhole perspective views
# Maintains camera alignment and optionally processes masks
#
# Author: Based on COLMAP fisheye conversion logic, adapted for Metashape
# Compatible with Metashape 2.2

import Metashape
import os
import sys
import math
import numpy as np
import time
import traceback
from datetime import datetime

# Checking compatibility
compatible_major_version = "2.2"
found_major_version = ".".join(Metashape.app.version.split('.')[:2])
if found_major_version != compatible_major_version:
    raise Exception("Incompatible Metashape version: {} != {}".format(found_major_version, compatible_major_version))

# Try to import required libraries
try:
    import cv2
    print("OpenCV version: {}".format(cv2.__version__))
except ImportError as e:
    print("Error: OpenCV (cv2) is required but not installed.")
    print("Please install it using: pip install opencv-python")
    raise e

# Try to import PyQt5 for GUI
USE_GUI = False
try:
    from PySide2 import QtGui, QtCore, QtWidgets
    from PySide2.QtCore import Qt, QThread, Signal
    USE_GUI = True
    print("GUI mode available")
except ImportError:
    print("PySide2 not available. Running in console mode.")

# Perspective view configurations (matching COLMAP script)
DEFAULT_VIEWS = [
    {"name": "view_p45_r35", "yaw": 0.0, "pitch": 45.0, "roll": 35.3},
    {"name": "view_n45_r35", "yaw": 0.0, "pitch": -45.0, "roll": 35.3},
    {"name": "view_yL45_n54", "yaw": -45.0, "pitch": 0.0, "roll": -54.7}
]

class FisheyeConverter:
    """Core fisheye to pinhole conversion logic"""
    
    def __init__(self):
        self.output_width = 1920
        self.output_height = 1920
        self.target_fov = 90.0  # degrees
        self.views = DEFAULT_VIEWS
        self.process_masks = True
        self.realign_after = False
        self.remove_originals = False
        self.process_all_cameras = False  # Option to process all cameras regardless of type
        
    def calculate_rotation_matrix(self, yaw_deg, pitch_deg, roll_deg):
        """Calculate rotation matrix from Euler angles (ZYX convention)"""
        yaw = math.radians(yaw_deg)
        pitch = math.radians(pitch_deg)
        roll = math.radians(roll_deg)
        
        # Individual rotation matrices
        Rz = np.array([
            [math.cos(yaw), -math.sin(yaw), 0],
            [math.sin(yaw), math.cos(yaw), 0],
            [0, 0, 1]
        ])
        
        Ry = np.array([
            [math.cos(pitch), 0, math.sin(pitch)],
            [0, 1, 0],
            [-math.sin(pitch), 0, math.cos(pitch)]
        ])
        
        Rx = np.array([
            [1, 0, 0],
            [0, math.cos(roll), -math.sin(roll)],
            [0, math.sin(roll), math.cos(roll)]
        ])
        
        # Combined rotation (ZYX order)
        R = Rz @ Ry @ Rx
        return R
    
    def get_pinhole_intrinsics(self):
        """Calculate pinhole camera intrinsics from FOV"""
        fov_rad = math.radians(self.target_fov)
        fx = (self.output_width / 2.0) / math.tan(fov_rad / 2.0)
        fy = fx  # Square pixels
        cx = self.output_width / 2.0
        cy = self.output_height / 2.0
        return fx, fy, cx, cy
    
    def extract_fisheye_calibration(self, camera):
        """Extract fisheye calibration parameters from Metashape camera"""
        try:
            sensor = camera.sensor
            if not sensor:
                print("  Warning: No sensor for camera {}".format(camera.label))
                return None
                
            # Get image dimensions
            width = sensor.width
            height = sensor.height
            
            if not sensor.calibration:
                # Use default calibration based on sensor
                print("  Using default calibration for {}".format(camera.label))
                fx = sensor.focal_length if sensor.focal_length else width / 2.0
                fy = fx
                cx = width / 2.0
                cy = height / 2.0
                return {
                    'width': width,
                    'height': height,
                    'fx': fx,
                    'fy': fy,
                    'cx': cx,
                    'cy': cy,
                    'k1': 0,
                    'k2': 0,
                    'k3': 0,
                    'k4': 0
                }
            
            calib = sensor.calibration
            
            # Extract calibration parameters
            # Metashape stores focal length in pixels already, not normalized
            if hasattr(calib, 'f'):
                fx = calib.f
            else:
                # Fallback: use sensor focal length
                fx = sensor.focal_length
                
            # Handle aspect ratio
            if hasattr(calib, 'b1'):
                fy = fx * (1 + calib.b1)
            else:
                fy = fx
                
            # Principal point - Metashape uses pixel coordinates relative to image center
            if hasattr(calib, 'cx') and hasattr(calib, 'cy'):
                cx = width / 2.0 + calib.cx
                cy = height / 2.0 + calib.cy
            else:
                cx = width / 2.0
                cy = height / 2.0
            
            # Distortion coefficients (k1, k2, p1, p2, k3, k4)
            # For fisheye, we typically use k1-k4
            k1 = calib.k1 if hasattr(calib, 'k1') else 0
            k2 = calib.k2 if hasattr(calib, 'k2') else 0
            k3 = calib.k3 if hasattr(calib, 'k3') else 0
            k4 = calib.k4 if hasattr(calib, 'k4') else 0
            
            return {
                'width': width,
                'height': height,
                'fx': fx,
                'fy': fy,
                'cx': cx,
                'cy': cy,
                'k1': k1,
                'k2': k2,
                'k3': k3,
                'k4': k4
            }
        except Exception as e:
            print("  Error extracting calibration for {}: {}".format(camera.label, str(e)))
            import traceback
            traceback.print_exc()
            return None
    
    def dewarp_fisheye_image(self, image, fisheye_calib, view_rotation):
        """Dewarp fisheye image to perspective view"""
        try:
            # Check if we have very small focal length (might be normalized)
            if fisheye_calib['fx'] < 10:
                print("    Warning: Very small focal length detected ({}), scaling up".format(fisheye_calib['fx']))
                # Assume it's normalized, convert to pixels
                fisheye_calib['fx'] *= fisheye_calib['width']
                fisheye_calib['fy'] *= fisheye_calib['height']
            
            # Fisheye camera matrix
            K_fisheye = np.array([
                [fisheye_calib['fx'], 0, fisheye_calib['cx']],
                [0, fisheye_calib['fy'], fisheye_calib['cy']],
                [0, 0, 1]
            ], dtype=np.float32)
            
            # Fisheye distortion coefficients
            D_fisheye = np.array([
                fisheye_calib['k1'],
                fisheye_calib['k2'],
                fisheye_calib['k3'],
                fisheye_calib['k4']
            ], dtype=np.float32)
            
            # Pinhole camera matrix
            fx, fy, cx, cy = self.get_pinhole_intrinsics()
            K_pinhole = np.array([
                [fx, 0, cx],
                [0, fy, cy],
                [0, 0, 1]
            ], dtype=np.float32)
            
            # Convert rotation matrix to float32
            view_rotation_f32 = view_rotation.astype(np.float32)
            
            # Debug print
            print("    Fisheye K matrix:\n", K_fisheye)
            print("    Distortion coeffs:", D_fisheye)
            print("    Pinhole K matrix:\n", K_pinhole)
            print("    Output size: {}x{}".format(self.output_width, self.output_height))
            
            # Check if distortion is too high (might indicate wrong model)
            if abs(fisheye_calib['k1']) > 10:
                print("    Warning: Very high distortion coefficient k1={}".format(fisheye_calib['k1']))
                print("    This might indicate a different fisheye model")
            
            # Generate undistortion maps with CV_32FC1 to avoid integer overflow
            map1, map2 = cv2.fisheye.initUndistortRectifyMap(
                K_fisheye,
                D_fisheye,
                view_rotation_f32,
                K_pinhole,
                (self.output_width, self.output_height),
                cv2.CV_32FC1  # Use float instead of short integer
            )
            
            # Check if maps are valid
            if np.all(map1 < 0) or np.all(map1 > fisheye_calib['width']):
                print("    Warning: Invalid mapping detected, all pixels mapping outside image")
                print("    Map1 range: {} to {}".format(np.min(map1), np.max(map1)))
                print("    Map2 range: {} to {}".format(np.min(map2), np.max(map2)))
            
            # Remap the image
            dewarped = cv2.remap(image, map1, map2, cv2.INTER_LINEAR, 
                               borderMode=cv2.BORDER_CONSTANT, borderValue=(0, 0, 0))
            
            # Check if result is all black
            if np.max(dewarped) == 0:
                print("    Warning: Output image is completely black!")
                # Try a simple center crop as fallback
                h, w = image.shape[:2]
                crop_size = min(h, w) // 2
                y_start = (h - crop_size) // 2
                x_start = (w - crop_size) // 2
                cropped = image[y_start:y_start+crop_size, x_start:x_start+crop_size]
                dewarped = cv2.resize(cropped, (self.output_width, self.output_height))
                print("    Using center crop as fallback")
            
            return dewarped, (map1, map2)
        except Exception as e:
            print("    Error in dewarp_fisheye_image: {}".format(str(e)))
            import traceback
            traceback.print_exc()
            raise
    
    def dewarp_mask(self, mask, maps):
        """Dewarp a binary mask using pre-calculated maps"""
        map1, map2 = maps
        dewarped_mask = cv2.remap(mask, map1, map2, cv2.INTER_NEAREST,
                                borderMode=cv2.BORDER_CONSTANT, borderValue=0)
        _, binary_mask = cv2.threshold(dewarped_mask, 127, 255, cv2.THRESH_BINARY)
        return binary_mask
    
    def is_fisheye_camera(self, camera):
        """Check if camera is fisheye based on sensor type or label"""
        if not camera.sensor:
            return False
            
        # Check sensor type first
        if hasattr(camera.sensor, 'type') and camera.sensor.type == Metashape.Sensor.Type.Fisheye:
            return True
            
        # Check for fisheye calibration model
        if camera.sensor.calibration:
            calib = camera.sensor.calibration
            # Check if using fisheye projection (high distortion coefficients or specific type)
            if hasattr(calib, 'type') and calib.type == Metashape.Sensor.Type.Fisheye:
                return True
                
        # Fallback: Check sensor/camera labels
        sensor_label = camera.sensor.label.lower() if camera.sensor.label else ""
        camera_label = camera.label.lower() if camera.label else ""
        
        fisheye_keywords = ['fisheye', 'fish-eye', 'fish eye', 'fe', 'omnidirectional', 'circular']
        
        for keyword in fisheye_keywords:
            if keyword in sensor_label or keyword in camera_label:
                return True
                
        # Additional check: Look for high distortion values typical of fisheye
        if camera.sensor.calibration:
            calib = camera.sensor.calibration
            # Fisheye cameras typically have significant k1 distortion
            if hasattr(calib, 'k1') and abs(calib.k1) > 0.1:
                return True
                
        return False
    
    def process_chunk(self, chunk, output_dir, progress_callback=None):
        """Process all fisheye cameras in a chunk"""
        # Find cameras to process
        cameras_to_process = []
        
        if self.process_all_cameras:
            # Process all aligned cameras
            for camera in chunk.cameras:
                if camera.transform and camera.photo:
                    cameras_to_process.append(camera)
            print("Processing all {} aligned cameras".format(len(cameras_to_process)))
        else:
            # Find only fisheye cameras
            for camera in chunk.cameras:
                if self.is_fisheye_camera(camera) and camera.transform:
                    cameras_to_process.append(camera)
            print("Found {} fisheye cameras to process".format(len(cameras_to_process)))
        
        if not cameras_to_process:
            raise Exception("No suitable cameras found in the chunk. Try enabling 'Process all cameras' option.")
        
        fisheye_cameras = cameras_to_process  # Keep variable name for compatibility
        
        # Create output directories
        images_dir = os.path.join(output_dir, "images")
        os.makedirs(images_dir, exist_ok=True)
        
        masks_dir = None
        if self.process_masks:
            masks_dir = os.path.join(output_dir, "masks")
            os.makedirs(masks_dir, exist_ok=True)
        
        # Create pinhole sensors for each fisheye sensor group
        pinhole_sensors = {}  # Map from fisheye sensor to pinhole sensor
        
        # Process each fisheye camera
        new_cameras = []
        total_operations = len(fisheye_cameras) * len(self.views)
        current_operation = 0
        
        for cam_idx, fisheye_cam in enumerate(fisheye_cameras):
            print("\nProcessing fisheye camera: {} ({}/{})".format(
                fisheye_cam.label, cam_idx + 1, len(fisheye_cameras)))
            print("  Sensor: {}".format(fisheye_cam.sensor.label if fisheye_cam.sensor else "None"))
            
            # Extract fisheye calibration
            fisheye_calib = self.extract_fisheye_calibration(fisheye_cam)
            if not fisheye_calib:
                print("  Warning: Could not extract calibration for {}".format(fisheye_cam.label))
                continue
            
            # Load fisheye image
            if not fisheye_cam.photo or not fisheye_cam.photo.path:
                print("  Warning: No photo path for {}".format(fisheye_cam.label))
                continue
                
            fisheye_img_path = fisheye_cam.photo.path
            if not os.path.exists(fisheye_img_path):
                print("  Warning: Image file not found: {}".format(fisheye_img_path))
                continue
                
            fisheye_img = cv2.imread(fisheye_img_path)
            if fisheye_img is None:
                print("  Warning: Could not load image: {}".format(fisheye_img_path))
                continue
            
            # Load mask if available
            fisheye_mask = None
            if self.process_masks and fisheye_cam.mask:
                # Skip mask processing for now - it's causing issues
                print("  Skipping mask processing (not critical for conversion)")
            
            # Get fisheye camera transform
            T_fisheye = fisheye_cam.transform
            
            # Process each view
            for view in self.views:
                current_operation += 1
                if progress_callback:
                    progress_callback(current_operation, total_operations)
                
                print("  Creating view: {}".format(view['name']))
                
                # Calculate view rotation matrix
                R_view = self.calculate_rotation_matrix(view['yaw'], view['pitch'], view['roll'])
                
                # Dewarp image
                dewarped_img, maps = self.dewarp_fisheye_image(fisheye_img, fisheye_calib, R_view)
                
                # Save dewarped image
                base_name = os.path.splitext(os.path.basename(fisheye_img_path))[0]
                new_img_name = "{}_{}.jpg".format(base_name, view['name'])
                new_img_path = os.path.join(images_dir, new_img_name)
                cv2.imwrite(new_img_path, dewarped_img, [cv2.IMWRITE_JPEG_QUALITY, 95])
                
                # Process mask if available
                new_mask_path = None
                if fisheye_mask is not None and masks_dir:
                    dewarped_mask = self.dewarp_mask(fisheye_mask, maps)
                    new_mask_name = "{}_{}.png".format(base_name, view['name'])
                    new_mask_path = os.path.join(masks_dir, new_mask_name)
                    cv2.imwrite(new_mask_path, dewarped_mask)
                
                # Add new camera to chunk
                chunk.addPhotos([new_img_path])
                new_cam = chunk.cameras[-1]  # Get the newly added camera
                new_cam.label = new_img_name
                
                # Get or create pinhole sensor for this fisheye sensor group
                fisheye_sensor = fisheye_cam.sensor
                if fisheye_sensor not in pinhole_sensors:
                    # Create new pinhole sensor for this fisheye sensor group
                    pinhole_sensor = chunk.addSensor()
                    pinhole_sensor.label = "Pinhole from {}".format(fisheye_sensor.label)
                    pinhole_sensor.type = Metashape.Sensor.Type.Frame
                    
                    # Set pinhole calibration
                    fx, fy, cx, cy = self.get_pinhole_intrinsics()
                    calib = Metashape.Calibration()
                    calib.type = Metashape.Sensor.Type.Frame
                    calib.width = self.output_width
                    calib.height = self.output_height
                    calib.f = fx
                    calib.cx = (cx - self.output_width / 2.0) / self.output_width
                    calib.cy = (cy - self.output_height / 2.0) / self.output_height
                    calib.b1 = 0  # No aspect ratio difference
                    calib.k1 = 0  # No distortion
                    calib.k2 = 0
                    calib.k3 = 0
                    calib.k4 = 0
                    pinhole_sensor.calibration = calib
                    pinhole_sensor.width = self.output_width
                    pinhole_sensor.height = self.output_height
                    pinhole_sensor.focal_length = fx
                    pinhole_sensor.pixel_width = 1
                    pinhole_sensor.pixel_height = 1
                    
                    pinhole_sensors[fisheye_sensor] = pinhole_sensor
                    print("  Created pinhole sensor: {}".format(pinhole_sensor.label))
                
                new_cam.sensor = pinhole_sensors[fisheye_sensor]
                
                # Calculate new camera transform
                # Extract rotation and translation from fisheye camera
                R_fisheye = Metashape.Matrix([
                    [T_fisheye[0,0], T_fisheye[0,1], T_fisheye[0,2]],
                    [T_fisheye[1,0], T_fisheye[1,1], T_fisheye[1,2]],
                    [T_fisheye[2,0], T_fisheye[2,1], T_fisheye[2,2]]
                ])
                t_fisheye = Metashape.Vector([T_fisheye[0,3], T_fisheye[1,3], T_fisheye[2,3]])
                
                # Convert numpy rotation to Metashape Matrix
                R_view_meta = Metashape.Matrix([
                    [R_view[0,0], R_view[0,1], R_view[0,2]],
                    [R_view[1,0], R_view[1,1], R_view[1,2]],
                    [R_view[2,0], R_view[2,1], R_view[2,2]]
                ])
                
                # New rotation = R_view @ R_fisheye (matching COLMAP logic)
                R_new = R_view_meta * R_fisheye
                
                # New translation = R_view @ t_fisheye  
                t_new = R_view_meta * t_fisheye
                
                # Construct new transform matrix
                new_transform = Metashape.Matrix([
                    [R_new[0,0], R_new[0,1], R_new[0,2], t_new[0]],
                    [R_new[1,0], R_new[1,1], R_new[1,2], t_new[1]],
                    [R_new[2,0], R_new[2,1], R_new[2,2], t_new[2]],
                    [0, 0, 0, 1]
                ])
                
                new_cam.transform = new_transform
                
                # Apply mask if created
                if new_mask_path:
                    mask = Metashape.Mask()
                    mask.load(new_mask_path)
                    new_cam.mask = mask
                
                new_cameras.append(new_cam)
        
        print("\nCreated {} new pinhole cameras".format(len(new_cameras)))
        
        # Optional: Realign cameras
        if self.realign_after and new_cameras:
            print("\nRealigning cameras...")
            # Enable only new cameras for alignment
            for cam in chunk.cameras:
                cam.enabled = cam in new_cameras
            
            chunk.alignCameras()
            
            # Re-enable all cameras
            for cam in chunk.cameras:
                cam.enabled = True
        
        # Optional: Remove original fisheye cameras
        if self.remove_originals:
            print("\nRemoving original fisheye cameras...")
            for cam in fisheye_cameras:
                chunk.remove(cam)
        
        return len(new_cameras)


# GUI Implementation
if USE_GUI:
    class ProcessingThread(QThread):
        progress = Signal(int, int)
        finished = Signal(int)
        error = Signal(str)
        
        def __init__(self, converter, chunk, output_dir):
            super().__init__()
            self.converter = converter
            self.chunk = chunk
            self.output_dir = output_dir
            
        def run(self):
            try:
                def progress_callback(current, total):
                    self.progress.emit(current, total)
                
                result = self.converter.process_chunk(
                    self.chunk, 
                    self.output_dir,
                    progress_callback
                )
                self.finished.emit(result)
            except Exception as e:
                self.error.emit(str(e))
    
    class FisheyeToPinholeDialog(QtWidgets.QDialog):
        def __init__(self, parent=None):
            super().__init__(parent)
            self.converter = FisheyeConverter()
            self.processing_thread = None
            self.init_ui()
            
        def init_ui(self):
            self.setWindowTitle("Fisheye to Pinhole Converter")
            self.setMinimumWidth(500)
            
            layout = QtWidgets.QVBoxLayout()
            
            # Output directory selection
            dir_group = QtWidgets.QGroupBox("Output Directory")
            dir_layout = QtWidgets.QHBoxLayout()
            self.dir_edit = QtWidgets.QLineEdit()
            self.dir_button = QtWidgets.QPushButton("Browse...")
            self.dir_button.clicked.connect(self.browse_directory)
            dir_layout.addWidget(self.dir_edit)
            dir_layout.addWidget(self.dir_button)
            dir_group.setLayout(dir_layout)
            layout.addWidget(dir_group)
            
            # Image settings
            img_group = QtWidgets.QGroupBox("Image Settings")
            img_layout = QtWidgets.QGridLayout()
            
            img_layout.addWidget(QtWidgets.QLabel("Output Width:"), 0, 0)
            self.width_spin = QtWidgets.QSpinBox()
            self.width_spin.setRange(100, 8000)
            self.width_spin.setValue(1920)
            self.width_spin.valueChanged.connect(self.update_settings)
            img_layout.addWidget(self.width_spin, 0, 1)
            
            img_layout.addWidget(QtWidgets.QLabel("Output Height:"), 1, 0)
            self.height_spin = QtWidgets.QSpinBox()
            self.height_spin.setRange(100, 8000)
            self.height_spin.setValue(1920)
            self.height_spin.valueChanged.connect(self.update_settings)
            img_layout.addWidget(self.height_spin, 1, 1)
            
            img_layout.addWidget(QtWidgets.QLabel("Field of View (°):"), 2, 0)
            self.fov_spin = QtWidgets.QDoubleSpinBox()
            self.fov_spin.setRange(10.0, 170.0)
            self.fov_spin.setValue(90.0)
            self.fov_spin.setSingleStep(5.0)
            self.fov_spin.valueChanged.connect(self.update_settings)
            img_layout.addWidget(self.fov_spin, 2, 1)
            
            img_group.setLayout(img_layout)
            layout.addWidget(img_group)
            
            # Processing options
            opt_group = QtWidgets.QGroupBox("Processing Options")
            opt_layout = QtWidgets.QVBoxLayout()
            
            self.all_cameras_check = QtWidgets.QCheckBox("Process all cameras (not just fisheye)")
            self.all_cameras_check.setChecked(False)
            self.all_cameras_check.stateChanged.connect(self.update_settings)
            opt_layout.addWidget(self.all_cameras_check)
            
            self.mask_check = QtWidgets.QCheckBox("Process masks")
            self.mask_check.setChecked(True)
            self.mask_check.stateChanged.connect(self.update_settings)
            opt_layout.addWidget(self.mask_check)
            
            self.realign_check = QtWidgets.QCheckBox("Realign cameras after conversion")
            self.realign_check.setChecked(False)
            self.realign_check.stateChanged.connect(self.update_settings)
            opt_layout.addWidget(self.realign_check)
            
            self.remove_check = QtWidgets.QCheckBox("Remove original fisheye cameras")
            self.remove_check.setChecked(False)
            self.remove_check.stateChanged.connect(self.update_settings)
            opt_layout.addWidget(self.remove_check)
            
            opt_group.setLayout(opt_layout)
            layout.addWidget(opt_group)
            
            # Progress bar
            self.progress_bar = QtWidgets.QProgressBar()
            self.progress_bar.setVisible(False)
            layout.addWidget(self.progress_bar)
            
            # Status label
            self.status_label = QtWidgets.QLabel("")
            layout.addWidget(self.status_label)
            
            # Buttons
            button_layout = QtWidgets.QHBoxLayout()
            self.process_button = QtWidgets.QPushButton("Start Processing")
            self.process_button.clicked.connect(self.start_processing)
            self.cancel_button = QtWidgets.QPushButton("Cancel")
            self.cancel_button.clicked.connect(self.reject)
            button_layout.addWidget(self.process_button)
            button_layout.addWidget(self.cancel_button)
            layout.addLayout(button_layout)
            
            self.setLayout(layout)
            
        def browse_directory(self):
            dir_path = QtWidgets.QFileDialog.getExistingDirectory(
                self, "Select Output Directory")
            if dir_path:
                self.dir_edit.setText(dir_path)
                
        def update_settings(self):
            self.converter.output_width = self.width_spin.value()
            self.converter.output_height = self.height_spin.value()
            self.converter.target_fov = self.fov_spin.value()
            self.converter.process_all_cameras = self.all_cameras_check.isChecked()
            self.converter.process_masks = self.mask_check.isChecked()
            self.converter.realign_after = self.realign_check.isChecked()
            self.converter.remove_originals = self.remove_check.isChecked()
            
        def start_processing(self):
            output_dir = self.dir_edit.text()
            if not output_dir:
                QtWidgets.QMessageBox.warning(
                    self, "Warning", "Please select an output directory")
                return
                
            chunk = Metashape.app.document.chunk
            if not chunk:
                QtWidgets.QMessageBox.warning(
                    self, "Warning", "No active chunk found")
                return
                
            # Disable UI during processing
            self.process_button.setEnabled(False)
            self.dir_button.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.status_label.setText("Processing...")
            
            # Start processing thread
            self.processing_thread = ProcessingThread(
                self.converter, chunk, output_dir)
            self.processing_thread.progress.connect(self.update_progress)
            self.processing_thread.finished.connect(self.processing_finished)
            self.processing_thread.error.connect(self.processing_error)
            self.processing_thread.start()
            
        def update_progress(self, current, total):
            self.progress_bar.setMaximum(total)
            self.progress_bar.setValue(current)
            percent = int(100 * current / total)
            self.status_label.setText("Processing... {}%".format(percent))
            
        def processing_finished(self, num_cameras):
            self.progress_bar.setVisible(False)
            self.status_label.setText(
                "Processing complete! Created {} pinhole cameras.".format(num_cameras))
            QtWidgets.QMessageBox.information(
                self, "Success", 
                "Successfully created {} pinhole cameras from fisheye images.".format(num_cameras))
            self.accept()
            
        def processing_error(self, error_msg):
            self.progress_bar.setVisible(False)
            self.process_button.setEnabled(True)
            self.dir_button.setEnabled(True)
            self.status_label.setText("Error occurred")
            QtWidgets.QMessageBox.critical(
                self, "Error", "Processing failed:\n{}".format(error_msg))


# Console mode implementation
def run_console_mode():
    """Run the script in console mode"""
    print("\nFisheye to Pinhole Converter - Console Mode")
    print("-" * 50)
    
    chunk = Metashape.app.document.chunk
    if not chunk:
        print("Error: No active chunk found")
        return
        
    converter = FisheyeConverter()
    
    # Get output directory
    output_dir = Metashape.app.getExistingDirectory("Select output directory for pinhole images")
    if not output_dir:
        print("Cancelled by user")
        return
        
    # Get settings via dialog
    settings = [
        ("Output image width:", converter.output_width),
        ("Output image height:", converter.output_height),
        ("Field of view (degrees):", converter.target_fov),
        ("Process all cameras:", converter.process_all_cameras),
        ("Process masks:", converter.process_masks),
        ("Realign after conversion:", converter.realign_after),
        ("Remove original cameras:", converter.remove_originals)
    ]
    
    labels = [s[0] for s in settings]
    values = [str(s[1]) for s in settings]
    
    result = Metashape.app.getMultipleStrings(
        "Fisheye to Pinhole Settings", labels, values)
    
    if not result:
        print("Cancelled by user")
        return
        
    # Parse settings
    try:
        converter.output_width = int(result[0])
        converter.output_height = int(result[1])
        converter.target_fov = float(result[2])
        converter.process_all_cameras = result[3].lower() in ['true', '1', 'yes']
        converter.process_masks = result[4].lower() in ['true', '1', 'yes']
        converter.realign_after = result[5].lower() in ['true', '1', 'yes']
        converter.remove_originals = result[6].lower() in ['true', '1', 'yes']
    except ValueError as e:
        print("Error parsing settings: {}".format(e))
        return
    
    # Process
    print("\nStarting conversion...")
    print("Output directory: {}".format(output_dir))
    print("Image size: {}x{}".format(converter.output_width, converter.output_height))
    print("Field of view: {}°".format(converter.target_fov))
    
    try:
        start_time = time.time()
        num_cameras = converter.process_chunk(chunk, output_dir)
        elapsed = time.time() - start_time
        
        print("\nSuccess! Created {} pinhole cameras in {:.1f} seconds".format(
            num_cameras, elapsed))
        
        Metashape.app.messageBox(
            "Successfully created {} pinhole cameras from fisheye images.".format(num_cameras))
            
    except Exception as e:
        print("\nError during processing: {}".format(e))
        print(traceback.format_exc())
        Metashape.app.messageBox("Error: {}".format(str(e)))


# Main entry point
def main():
    """Main entry point for the script"""
    print("\nFisheye to Pinhole Converter for Metashape")
    print("Version 1.0")
    print("=" * 50)
    
    if USE_GUI:
        app = QtWidgets.QApplication.instance()
        parent = app.activeWindow()
        
        dialog = FisheyeToPinholeDialog(parent)
        dialog.exec_()
    else:
        run_console_mode()


# Register with Metashape menu if imported as module
if __name__ == "__main__":
    main()
else:
    # Add menu item
    label = "Scripts/Convert Fisheye to Pinhole"
    Metashape.app.addMenuItem(label, main)
    print("Added menu item: {}".format(label))