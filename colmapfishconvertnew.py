import cv2
import numpy as np
import math
import argparse
import os
import shutil
# import subprocess # Removed for BIN conversion
from collections import defaultdict
from scipy.spatial.transform import Rotation as R_scipy

# --- COLMAP Read/Write Utilities (Simplified) ---
def read_colmap_cameras(path):
    cameras = {}
    with open(path, "r") as f:
        for line in f:
            if line.startswith("#"):
                continue
            parts = line.strip().split()
            if not parts: continue
            try:
                camera_id = int(parts[0])
                model = parts[1]
                width = int(parts[2])
                height = int(parts[3])
                params = [float(p) for p in parts[4:]]
                cameras[camera_id] = {"model": model, "width": width, "height": height, "params": params}
            except (ValueError, IndexError) as e:
                print(f"Warning: Could not parse camera line: '{line.strip()}'. Error: {e}")
                continue
    return cameras

def read_colmap_images(path):
    images = {}
    with open(path, "r") as f:
        image_lines = [line for line in f if not line.startswith("#")]
        i = 0
        while i < len(image_lines):
            line1_parts = image_lines[i].strip().split()
            if not line1_parts: i+=1; continue 
            try:
                image_id = int(line1_parts[0])
                qw, qx, qy, qz = float(line1_parts[1]), float(line1_parts[2]), float(line1_parts[3]), float(line1_parts[4])
                tx, ty, tz = float(line1_parts[5]), float(line1_parts[6]), float(line1_parts[7])
                camera_id = int(line1_parts[8])
                name = line1_parts[9]
            except (ValueError, IndexError) as e:
                print(f"Warning: Could not parse image line 1: '{image_lines[i].strip()}'. Error: {e}")
                i += 2 
                continue
            
            i += 1
            if i >= len(image_lines): 
                print(f"Warning: Missing points2D line for image ID {image_id}, name {name}."); break
            
            line2_parts = image_lines[i].strip().split()
            points2D = []
            if len(line2_parts) % 3 == 0: 
                for j in range(0, len(line2_parts), 3):
                    try:
                        p_x, p_y = float(line2_parts[j]), float(line2_parts[j+1])
                        p3d_id = int(line2_parts[j+2])
                        points2D.append((p_x, p_y, p3d_id))
                    except ValueError:
                        print(f"Warning: Malformed points2D data for image {name}, part: {line2_parts[j:j+3]}")
                        continue 
            elif line2_parts: 
                 print(f"Warning: points2D line for image {name} has unexpected number of elements: {len(line2_parts)}")

            images[image_id] = {
                "qvec": np.array([qw, qx, qy, qz]),
                "tvec": np.array([tx, ty, tz]),
                "camera_id": camera_id,
                "name": name,
                "points2D": points2D,
            }
            i += 1
    return images

def read_colmap_points3D(path):
    points3D = {}
    with open(path, "r") as f:
        for line in f:
            if line.startswith("#"):
                continue
            parts = line.strip().split()
            if not parts: 
                continue
            try:
                point3D_id = int(parts[0])
                xyz = np.array([float(parts[1]), float(parts[2]), float(parts[3])])
                rgb = np.array([int(parts[4]), int(parts[5]), int(parts[6])])
                error = float(parts[7])
                track_len = int(parts[8])
                
                track = []
                track_data_start_index = 9
                for i_track in range(track_len): 
                    image_id_idx = track_data_start_index + (i_track * 2)
                    point2d_idx_idx = track_data_start_index + (i_track * 2) + 1
                    
                    if point2d_idx_idx < len(parts): 
                        track.append((int(parts[image_id_idx]), int(parts[point2d_idx_idx])))
                    else:
                        print(f"Warning: Truncated track data for Point3D ID {point3D_id}. Expected pair not found. Line: '{line.strip()}'")
                        break 
                points3D[point3D_id] = {"xyz": xyz, "rgb": rgb, "error": error, "track": track}
            except (ValueError, IndexError) as e:
                print(f"Warning: Could not parse line in points3D.txt: '{line.strip()}'. Error: {e}")
                continue
    return points3D

def write_colmap_cameras(path, cameras_data):
    with open(path, "w") as f:
        f.write("# Camera list with one line of data per camera:\n")
        f.write("#   CAMERA_ID, MODEL, WIDTH, HEIGHT, PARAMS[]\n")
        f.write(f"# Number of cameras: {len(cameras_data)}\n")
        for cam_id, data in sorted(cameras_data.items()):
            params_str = " ".join(map(str, data["params"]))
            f.write(f"{cam_id} {data['model']} {data['width']} {data['height']} {params_str}\n")

def write_colmap_images(path, images_data):
    with open(path, "w") as f:
        f.write("# Image list with two lines of data per image:\n")
        f.write("#   IMAGE_ID, QW, QX, QY, QZ, TX, TY, TZ, CAMERA_ID, NAME\n")
        f.write("#   POINTS2D[] as (X, Y, POINT3D_ID) (-1 for POINT3D_ID means no correspondence)\n")
        f.write(f"# Number of images: {len(images_data)}\n")
        for img_id, data in sorted(images_data.items()):
            qvec_str = " ".join(f"{x:.8f}" for x in data["qvec"])
            tvec_str = " ".join(f"{x:.8f}" for x in data["tvec"])
            f.write(f"{img_id} {qvec_str} {tvec_str} {data['camera_id']} {data['name']}\n")
            points_str_parts = []
            for p2d in data["points2D"]:
                points_str_parts.extend([f"{p2d[0]:.2f}", f"{p2d[1]:.2f}", str(p2d[2])])
            f.write(" ".join(points_str_parts) + "\n")

def write_colmap_points3D(path, points3D_data):
    with open(path, "w") as f:
        f.write("# 3D point list with one line of data per point:\n")
        f.write("#   POINT3D_ID, X, Y, Z, R, G, B, ERROR, TRACK_LENGTH, TRACK[] as (IMAGE_ID, POINT2D_IDX)\n")
        f.write(f"# Number of points: {len(points3D_data)}\n")
        for pt_id, data in sorted(points3D_data.items()):
            xyz_str = " ".join(f"{x:.6f}" for x in data["xyz"])
            rgb_str = " ".join(map(str, data["rgb"]))
            track_str_parts = []
            for t in data["track"]:
                track_str_parts.extend([str(t[0]), str(t[1])])
            track_str = " ".join(track_str_parts)
            f.write(f"{pt_id} {xyz_str} {rgb_str} {data['error']:.6f} {len(data['track'])} {track_str}\n")

# --- Dewarping and Transformation Utilities ---
def calculate_dewarp_rotation_matrix(yaw_deg, pitch_deg, roll_deg):
    yaw = math.radians(yaw_deg); pitch = math.radians(pitch_deg); roll = math.radians(roll_deg)
    Rz = np.array([[math.cos(yaw),-math.sin(yaw),0],[math.sin(yaw),math.cos(yaw),0],[0,0,1]])
    Ry = np.array([[math.cos(pitch),0,math.sin(pitch)],[0,1,0],[-math.sin(pitch),0,math.cos(pitch)]])
    Rx = np.array([[1,0,0],[0,math.cos(roll),-math.sin(roll)],[0,math.sin(roll),math.cos(roll)]])
    R_P_to_F = Rz @ Ry @ Rx 
    return R_P_to_F

def quaternion_to_rotation_matrix(qvec): 
    return R_scipy.from_quat([qvec[1], qvec[2], qvec[3], qvec[0]]).as_matrix()

def rotation_matrix_to_quaternion(R_mat): 
    quat_xyzw = R_scipy.from_matrix(R_mat).as_quat()
    return np.array([quat_xyzw[3], quat_xyzw[0], quat_xyzw[1], quat_xyzw[2]])

def project_points(points3D_world, R_wc, t_wc, K_persp_params, W_persp, H_persp):
    projected_points, valid_projection_mask = [], []
    if points3D_world.ndim == 1: points3D_world = points3D_world.reshape(1, 3)
    points_cam = (R_wc @ points3D_world.T + t_wc.reshape(3, 1)).T
    for p_cam in points_cam:
        if p_cam[2] <= 1e-3:
            projected_points.append([-1.0,-1.0]); valid_projection_mask.append(False); continue
        fx, cx, cy = K_persp_params; fy = fx 
        xp, yp = p_cam[0]/p_cam[2], p_cam[1]/p_cam[2]
        u, v = fx*xp+cx, fy*yp+cy
        if 0 <= u < W_persp and 0 <= v < H_persp:
            projected_points.append([u,v]); valid_projection_mask.append(True)
        else:
            projected_points.append([-1.0,-1.0]); valid_projection_mask.append(False)
    return np.array(projected_points), np.array(valid_projection_mask)

def main():
    parser = argparse.ArgumentParser(description="Convert COLMAP fisheye project to perspective views.")
    parser.add_argument("colmap_project_dir", help="Path to COLMAP project (must contain sparse/0 and image_folder_name).")
    parser.add_argument("output_dir", help="Path for the new project and generated images.")
    parser.add_argument("--output_img_width", type=int, default=1920)
    parser.add_argument("--output_img_height", type=int, default=1920)
    parser.add_argument("--image_folder_name", type=str, default="images", help="Name of the image folder within colmap_project_dir.")
    parser.add_argument("--input_masks_path", type=str, default=None, help="Optional: Path to input mask folder (relative to colmap_project_dir). Defaults to 'masks'.") 
    parser.add_argument("--output_image_folder_name", type=str, default="images", help="Name of the folder where perspective images will be stored in output_dir.")
    parser.add_argument("--output_masks_path", type=str, default=None, help="Optional: Path to output dewarped mask folder (relative to output_dir). Defaults to 'masks'.") 
    parser.add_argument("--reproject_points3d", action="store_true", help="If set, reprojects points3D.txt. Default is to copy original.")
    parser.add_argument("--skip_image_conversion", action="store_true", help="If set, skips dewarping images & masks (assumes they already exist).")
    parser.add_argument("--target_fx", type=float, default=None, help="Target focal length (fx) for perspective. Overrides FoV calc if set.")
    parser.add_argument("--target_fy", type=float, default=None, help="Target focal length (fy) for perspective. If None, uses target_fx.")
    parser.add_argument("--target_cx", type=float, default=None, help="Target principal point (cx) for perspective. Overrides calc if set.")
    parser.add_argument("--target_cy", type=float, default=None, help="Target principal point (cy) for perspective. Overrides calc if set.")
    parser.add_argument("--fov", type=float, default=90.0, help="Horizontal Field of View in degrees for perspective views, if target_fx is not provided (default: 90.0).")

    args = parser.parse_args()

    use_original_points3d = not args.reproject_points3d
    sparse_dir_orig = os.path.join(args.colmap_project_dir, "sparse", "0")
    images_dir_orig_abs = os.path.join(args.colmap_project_dir, args.image_folder_name)
    if not (os.path.isdir(sparse_dir_orig) and os.path.isdir(images_dir_orig_abs)):
        print(f"Error: sparse/0 or image folder not found. Check paths."); return

    actual_input_masks_path = args.input_masks_path if args.input_masks_path else "masks"
    input_masks_dir_abs = os.path.join(args.colmap_project_dir, actual_input_masks_path)
    process_masks = False
    if os.path.isdir(input_masks_dir_abs):
        process_masks = True; print(f"Input masks will be processed from: {input_masks_dir_abs}")
    elif args.input_masks_path : print(f"Warning: Specified input mask path '{input_masks_dir_abs}' not found. No mask processing.")
    else: print(f"Info: Default input mask folder '{actual_input_masks_path}' (relative to project dir) not found. No mask processing.")

    view_fov_deg = args.fov 
    target_views_params = [
        {"name":"view_p45_r35", "yaw":0.0, "pitch":45.0, "roll":35.3, "fov_deg": view_fov_deg},
        {"name":"view_n45_r35", "yaw":0.0, "pitch":-45.0, "roll":35.3, "fov_deg": view_fov_deg},
        {"name":"view_yL45_n54", "yaw":-45.0, "pitch":0.0, "roll":-54.7, "fov_deg": view_fov_deg},
    ]
    output_W, output_H = args.output_img_width, args.output_img_height
    os.makedirs(args.output_dir, exist_ok=True)
    new_images_storage_dir = os.path.join(args.output_dir, args.output_image_folder_name) 
    if not args.skip_image_conversion: os.makedirs(new_images_storage_dir, exist_ok=True)
    elif not os.path.isdir(new_images_storage_dir): print(f"Error: --skip_image_conversion set, but img dir '{new_images_storage_dir}' not found."); return
    
    new_masks_storage_dir = None 
    if process_masks and not args.skip_image_conversion : 
        actual_output_masks_path = args.output_masks_path if args.output_masks_path else "masks"
        new_masks_storage_dir = os.path.join(args.output_dir, actual_output_masks_path)
        os.makedirs(new_masks_storage_dir, exist_ok=True)
    
    new_colmap_sparse_dir = os.path.join(args.output_dir, "sparse", "0"); 
    os.makedirs(new_colmap_sparse_dir, exist_ok=True)

    print("Loading original COLMAP data..."); 
    cameras_orig = read_colmap_cameras(os.path.join(sparse_dir_orig, "cameras.txt"))
    images_orig = read_colmap_images(os.path.join(sparse_dir_orig, "images.txt"))
    points3D_orig = read_colmap_points3D(os.path.join(sparse_dir_orig, "points3D.txt"))
    
    new_cameras_data, new_images_data = {}, {}
    if not use_original_points3d: new_points3D_data = {pt_id: {**data, 'track':[]} for pt_id, data in points3D_orig.items()}

    if args.target_fx is not None:
        persp_fx = args.target_fx; persp_fy = args.target_fy if args.target_fy is not None else persp_fx 
        persp_cx = args.target_cx if args.target_cx is not None else output_W / 2.0
        persp_cy = args.target_cy if args.target_cy is not None else output_H / 2.0
        print(f"Using provided target intrinsics: fx={persp_fx}, fy={persp_fy}, cx={persp_cx}, cy={persp_cy}")
    else:
        fov_to_use = args.fov 
        fov_rad = math.radians(fov_to_use)
        persp_fx = (output_W / 2.0) / math.tan(fov_rad / 2.0); persp_fy = persp_fx 
        persp_cx = output_W / 2.0; persp_cy = output_H / 2.0
        print(f"Calculating intrinsics from FoV={fov_to_use}: fx/fy={persp_fx}, cx={persp_cx}, cy={persp_cy}")
    
    K_new_for_dewarp = np.array([[persp_fx,0,persp_cx],[0,persp_fy,persp_cy],[0,0,1]],dtype=np.float32)
    simple_pinhole_f = persp_fx 
    persp_cam_colmap_params = [simple_pinhole_f, persp_cx, persp_cy]
    shared_persp_cam_id = 1; new_cameras_data[shared_persp_cam_id] = {"model":"SIMPLE_PINHOLE","width":output_W,"height":output_H,"params":persp_cam_colmap_params}
    next_new_image_id = 1
    
    print("Processing images...")
    for orig_img_id, orig_img_data in images_orig.items():
        orig_cam_info = cameras_orig.get(orig_img_data["camera_id"])
        if not orig_cam_info: print(f"Warn: CamID {orig_img_data['camera_id']} for img {orig_img_data['name']} not found. Skip."); continue
        if orig_cam_info["model"] == "OPENCV_FISHEYE":
            print(f"  Processing fisheye: {orig_img_data['name']} (ID: {orig_img_id})")
            fisheye_img_path_colmap = orig_img_data['name']
            
            fisheye_mask, fisheye_mask_path_abs = None, None
            if process_masks and not args.skip_image_conversion: 
                mask_base, _ = os.path.splitext(fisheye_img_path_colmap)
                potential_mask_names = [f"{mask_base}.png", f"{mask_base}.jpg", f"{mask_base}.bmp", f"{mask_base}.PNG", f"{mask_base}.JPG", f"{mask_base}.BMP"]
                for mn in potential_mask_names:
                    pmp = os.path.join(input_masks_dir_abs, mn)
                    if os.path.exists(pmp): fisheye_mask_path_abs = pmp; break
                if fisheye_mask_path_abs:
                    fisheye_mask = cv2.imread(fisheye_mask_path_abs, cv2.IMREAD_GRAYSCALE)
                    if fisheye_mask is None: print(f"    Warn: Failed to load mask {fisheye_mask_path_abs}.")
            
            if not args.skip_image_conversion:
                fisheye_img_path_abs = os.path.join(images_dir_orig_abs, fisheye_img_path_colmap)
                if not os.path.exists(fisheye_img_path_abs): fisheye_img_path_abs = os.path.join(args.colmap_project_dir, fisheye_img_path_colmap)
                if not os.path.exists(fisheye_img_path_abs): print(f"    Warn: Src img '{orig_img_data['name']}' not found. Skip."); continue
                fisheye_img = cv2.imread(fisheye_img_path_abs)
                if fisheye_img is None: print(f"    Warn: Failed to load {fisheye_img_path_abs}. Skip."); continue
            
            K_fe = np.array([[orig_cam_info["params"][0],0,orig_cam_info["params"][2]],[0,orig_cam_info["params"][1],orig_cam_info["params"][3]],[0,0,1]],dtype=np.float32)
            D_fe = np.array(orig_cam_info["params"][4:],dtype=np.float32)
            R_wc_fe_orig = quaternion_to_rotation_matrix(orig_img_data["qvec"])
            t_wc_fe_orig = orig_img_data["tvec"]

            for view_params in target_views_params:
                R_P_to_F = calculate_dewarp_rotation_matrix(view_params["yaw"],view_params["pitch"],view_params["roll"])
                base,ext = os.path.splitext(os.path.basename(fisheye_img_path_colmap))
                new_img_fname = f"{base}_{view_params['name']}{ext}" 
                
                if not args.skip_image_conversion:
                    map1,map2 = cv2.fisheye.initUndistortRectifyMap(K_fe,D_fe,R_P_to_F,K_new_for_dewarp,(output_W,output_H),cv2.CV_16SC2)
                    persp_img = cv2.remap(fisheye_img,map1,map2,cv2.INTER_LINEAR,borderMode=cv2.BORDER_CONSTANT)
                    cv2.imwrite(os.path.join(new_images_storage_dir, new_img_fname), persp_img)
                    if fisheye_mask is not None and new_masks_storage_dir:
                        dewarped_mask = cv2.remap(fisheye_mask, map1, map2, cv2.INTER_NEAREST, borderMode=cv2.BORDER_CONSTANT, borderValue=(0))
                        _, dewarped_mask_binary = cv2.threshold(dewarped_mask, 127, 255, cv2.THRESH_BINARY)
                        mask_output_fname = f"{os.path.splitext(new_img_fname)[0]}.png" 
                        cv2.imwrite(os.path.join(new_masks_storage_dir, mask_output_fname), dewarped_mask_binary)
                
                new_img_path_colmap = new_img_fname 
                R_wc_persp_new = R_P_to_F @ R_wc_fe_orig 
                t_wc_persp_new = R_P_to_F @ t_wc_fe_orig 
                
                new_qvec, new_tvec = rotation_matrix_to_quaternion(R_wc_persp_new), t_wc_persp_new
                current_new_img_id = next_new_image_id
                new_images_data[current_new_img_id]={"qvec":new_qvec,"tvec":new_tvec,"camera_id":shared_persp_cam_id,"name":new_img_path_colmap,"points2D":[]}

                if not use_original_points3d:
                    for pt3D_id, pt3D_orig_data in points3D_orig.items():
                        if any(track_img_id == orig_img_id for track_img_id,_ in pt3D_orig_data["track"]):
                            xyz_world = pt3D_orig_data["xyz"]
                            uv_list,valid_list = project_points(xyz_world,R_wc_persp_new,t_wc_persp_new,persp_cam_colmap_params,output_W,output_H)
                            if valid_list[0]:
                                u,v=uv_list[0]
                                new_images_data[current_new_img_id]["points2D"].append((u,v,pt3D_id))
                                p2d_idx = len(new_images_data[current_new_img_id]["points2D"])-1
                                new_points3D_data[pt3D_id]["track"].append((current_new_img_id,p2d_idx))
                next_new_image_id += 1
        else: print(f"  Skip non-fisheye: {orig_img_data['name']}")

    print("Writing new COLMAP text files..."); 
    write_colmap_cameras(os.path.join(new_colmap_sparse_dir,"cameras.txt"),new_cameras_data)
    write_colmap_images(os.path.join(new_colmap_sparse_dir,"images.txt"),new_images_data)
    
    if use_original_points3d:
        shutil.copyfile(os.path.join(sparse_dir_orig, "points3D.txt"), os.path.join(new_colmap_sparse_dir, "points3D.txt"))
        print("Copied original points3D.txt.")
    else:
        write_colmap_points3D(os.path.join(new_colmap_sparse_dir,"points3D.txt"),new_points3D_data)
        print("Wrote new points3D.txt based on reprojections.")
        
    print(f"\nProcessing complete.")
    print(f"Perspective images are expected/saved in: {new_images_storage_dir}")
    if new_masks_storage_dir and process_masks and not args.skip_image_conversion: 
        print(f"Dewarped masks are saved in: {new_masks_storage_dir}")
    print(f"New COLMAP sparse model (text format) is in: {new_colmap_sparse_dir}")
    print(f"To convert to BIN format manually, cd to '{new_colmap_sparse_dir}' and run: colmap model_converter --input_path . --output_path . --output_type BIN")
    print(f"Ensure your image folder ('{args.output_image_folder_name}') is a sibling to 'sparse' within '{args.output_dir}' for standard COLMAP/GS loading (e.g. '{args.output_dir}/images' and '{args.output_dir}/sparse/0').")


if __name__ == "__main__":
    main()