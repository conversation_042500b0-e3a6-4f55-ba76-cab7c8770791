#!/usr/bin/env python3
"""
Simple test script to debug mask detection in Metashape
"""

import Metashape

def test_mask_detection():
    """Test different ways to detect masks in Metashape"""
    
    chunk = Metashape.app.document.chunk
    if not chunk:
        print("No active chunk!")
        return
    
    print("=== MASK DETECTION TEST ===")
    print(f"Total cameras: {len(chunk.cameras)}")
    
    # Count cameras with masks using different methods
    mask_count_method1 = 0
    mask_count_method2 = 0
    mask_count_method3 = 0
    
    for i, camera in enumerate(chunk.cameras):
        print(f"\nCamera {i}: {camera.label}")
        
        # Method 1: Direct camera.mask
        if hasattr(camera, 'mask') and camera.mask is not None:
            mask_count_method1 += 1
            print(f"  - camera.mask exists: {type(camera.mask)}")
            
            # Try to get mask info
            if hasattr(camera.mask, '__dict__'):
                print(f"    Mask attributes: {list(camera.mask.__dict__.keys())}")
        else:
            print("  - camera.mask: None or not present")
        
        # Method 2: Check photo.mask
        if camera.photo:
            if hasattr(camera.photo, 'mask') and camera.photo.mask is not None:
                mask_count_method2 += 1
                print(f"  - camera.photo.mask exists: {type(camera.photo.mask)}")
        
        # Method 3: Check for mask in photo meta
        if camera.photo and hasattr(camera.photo, 'meta'):
            if 'mask' in camera.photo.meta or 'mask_path' in camera.photo.meta:
                mask_count_method3 += 1
                print(f"  - Found mask info in photo.meta: {list(camera.photo.meta.keys())}")
        
        # Check if camera is enabled
        print(f"  - Camera enabled: {camera.enabled}")
        print(f"  - Camera type: {camera.type if hasattr(camera, 'type') else 'N/A'}")
        
        # Only check first 5 cameras for brevity
        if i >= 4:
            print("\n... (checking only first 5 cameras)")
            break
    
    print(f"\n=== SUMMARY ===")
    print(f"Masks found via camera.mask: {mask_count_method1}")
    print(f"Masks found via camera.photo.mask: {mask_count_method2}")
    print(f"Masks found via photo.meta: {mask_count_method3}")
    
    # Check chunk-level mask info
    print(f"\n=== CHUNK MASK INFO ===")
    if hasattr(chunk, 'masks'):
        print(f"chunk.masks exists: {chunk.masks}")
        if chunk.masks:
            print(f"Number of masks: {len(chunk.masks)}")
    
    if hasattr(chunk, 'mask_group'):
        print(f"chunk.mask_group exists: {chunk.mask_group}")
    
    # Try to export masks to see if that works
    print(f"\n=== TESTING MASK EXPORT ===")
    if hasattr(chunk, 'exportMasks'):
        print("chunk.exportMasks method exists")
        import tempfile
        import os
        
        # Try to export masks for first camera with a mask
        for camera in chunk.cameras[:5]:
            if hasattr(camera, 'mask') and camera.mask is not None:
                try:
                    with tempfile.TemporaryDirectory() as tmpdir:
                        print(f"Attempting to export mask for {camera.label} to {tmpdir}")
                        chunk.exportMasks(path=tmpdir + "/mask_{filename}.png", cameras=[camera])
                        
                        # Check what was created
                        files = os.listdir(tmpdir)
                        print(f"Files created: {files}")
                        
                        if files:
                            file_path = os.path.join(tmpdir, files[0])
                            file_size = os.path.getsize(file_path)
                            print(f"Mask file size: {file_size} bytes")
                    break
                except Exception as e:
                    print(f"Export failed: {e}")

if __name__ == "__main__":
    test_mask_detection()