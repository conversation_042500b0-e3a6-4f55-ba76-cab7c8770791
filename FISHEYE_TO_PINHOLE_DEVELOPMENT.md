# Fisheye to Pinhole Conversion Script Development Summary

## Overview
This document summarizes the development of scripts to convert fisheye cameras to pinhole views in Agisoft Metashape Pro, including successes, failures, and key learnings.

## Scripts Created

### 1. `fisheye_to_pinhole_unified.py` (Basic working version)
**Status**: ❌ Failed
- **Issues**: 
  - Camera-image mismatch when using `chunk.addPhotos()`
  - Metashape returns `None` when adding photos that may already exist
  - Very slow processing (sequential, no threading)
  - Path normalization issues between Windows/Unix systems

### 2. `fisheye_to_pinhole_unified_gui.py` (GUI version)
**Status**: ✅ Mostly Working (but slow)
- **Features**:
  - Clean GUI interface with PySide2
  - Output folder selection
  - Adjustable parameters (size, FOV)
  - Progress tracking
  - Option to disable original fisheye cameras
  - Retry logic for camera addition
  - Proper sensor creation (Pinhole_Back/Pinhole_Front)
- **Issues**:
  - Single-threaded processing is very slow
  - No option to skip existing images
  - Camera orientation still incorrect (inverse rotation issue)

### 3. `fisheye_to_pinhole_unified_fast.py` (Attempted optimization)
**Status**: ❌ Failed
- **Attempted improvements**:
  - Parallel image processing with ThreadPoolExecutor
  - Batch photo addition
  - Skip existing images option
- **Major failure**: 
  - `chunk.addPhotos()` returns `None` causing "object of type 'NoneType' has no len()" errors
  - Pre-checking for existing cameras caused more issues
  - Complex logic led to more failure points

### 4. `fisheye_to_pinhole_unified_batch.py` (Gemini-based approach)
**Status**: ❌ Failed
- **Approach**: Tried to replicate gemini script's batch processing
- **Issues**: Same `addPhotos` returning `None` problem

## Key Technical Issues Encountered

### 1. Camera Rotation/Orientation Problem
**Issue**: When applying rotation to images, camera poses need inverse rotation
- n45_r35 image looks bottom right → camera pose points top left ❌
- p45_r35 image looks bottom left → camera pose points top right ❌
- yL45_n54 image looks upward → camera pose points down ❌

**Solution**: Apply R_view^T (transpose/inverse) to camera pose when R_view is applied to image
```python
R_view_inv = R_view.T  # Transpose = inverse for rotation matrices
R_new = R_orig * R_view_inv_meta
```

### 2. Metashape API Issues
- `chunk.addPhotos()` behavior is unpredictable
- Returns `None` when photos already exist or fail to add
- Asynchronous behavior requires retry logic
- Path handling differs between operating systems

### 3. Performance Problems
- Sequential processing is very slow
- OpenCV operations are fast, but Metashape API calls are the bottleneck
- Threading helps with image processing but not with Metashape operations

## User Requirements Not Yet Met

1. **Speed**: Need parallel processing like `convert_to_cubemap_v012.py`
2. **Skip existing images**: Option to only re-align if images already processed
3. **Proper alignment**: Camera orientations still incorrect
4. **COLMAP export**: Black images in export (path or format issue?)

## Questions About Functionality

### Tie Points and Sparse Cloud
When disabling fisheye cameras:
- Tie points are **preserved** in the project
- Sparse cloud remains intact
- Disabled cameras won't be used in future processing
- This is the desired behavior ✅

### Refine Alignment
`chunk.alignCameras(cameras=new_cams, reset_alignment=False)`:
- Optimizes camera poses using existing tie points
- Does NOT create new tie points
- Adjusts positions/orientations for better alignment

### Black Images in COLMAP Export
Possible causes:
- Path encoding issues (forward vs backward slashes)
- Image format expectations (JPEG vs PNG)
- Metashape export bug with pinhole cameras
- Relative vs absolute path issues

## Comparison with Working Scripts

### `convert_to_cubemap_v012.py` (Spherical → Cubemap)
**Why it's fast**:
- Uses `concurrent.futures` for parallel processing
- Processes multiple images simultaneously
- Batch operations wherever possible

### `gemini_fisheye_to_pinhole_v2.py`
**Key differences**:
- Creates all views for one camera first
- Adds them in a single batch with `chunk.addPhotos(paths_to_add)`
- Different sensor naming scheme
- BUT: Has rotation issues and other bugs

## Recommended Next Steps

1. **Fix the working GUI version** (`fisheye_to_pinhole_unified_gui.py`):
   - Keep the inverse rotation fix
   - Add parallel image processing (but keep Metashape operations sequential)
   - Add skip existing functionality
   - Fix any remaining path issues

2. **Alternative approach**:
   - Process all images first (parallel)
   - Then add all photos in one large batch
   - Then configure all cameras
   - This minimizes Metashape API calls

3. **Debug COLMAP export**:
   - Check exact paths in exported files
   - Test with different image formats
   - Compare with working fisheye export

## Key Learnings

1. Metashape's `addPhotos()` is the main bottleneck and source of errors
2. Camera rotation requires inverse of image rotation
3. Batch operations are crucial for performance
4. Path handling must be extremely careful (OS differences)
5. Pre-checking for existing cameras can cause more problems than it solves
6. The API behavior differs between adding photos one-by-one vs in batches

## Latest Development: `fisheye_to_pinhole_unified_improved.py`

**Status**: ⚠️ Partially Working (with issues)

### What Works:
- ✅ Images process successfully
- ✅ Camera alignment appears correct
- ✅ New pinhole camera groups are created properly
- ✅ Rotation fix (R_view^T) is working correctly

### Issues Found:

1. **Performance**: 
   - Processing is still slow despite threading implementation
   - Threading may not be working as expected (needs investigation)

2. **Sensor Calibration Issues**:
   - Pixel size is set to 1x1 mm (incorrect - should be left blank)
   - Focal length shows as 960 mm (incorrect - should be left blank)
   - The "f" value of 960 pixels is correct
   - Fix: Remove lines setting `sensor.pixel_width`, `sensor.pixel_height`, and `sensor.focal_length`

3. **Refine Alignment Not Working**:
   - GUI option for "Refine alignment of new cameras" doesn't appear to do anything
   - The `alignCameras()` call may not be working properly

4. **Critical COLMAP Export Issue**:
   - When disabling original fisheyes and exporting in COLMAP format:
     - `points3D.bin` file is empty
     - Original tie points are lost
   - This suggests tie points are associated only with fisheye cameras
   - Need to investigate how `convert_to_cubemap_v012.py` handles this

### Code Fixes Needed:

1. **Remove incorrect sensor properties**:
```python
# Remove these lines from find_or_create_sensor():
sensor.pixel_width = 1      # DELETE THIS
sensor.pixel_height = 1     # DELETE THIS  
sensor.focal_length = pinhole_fx  # DELETE THIS
```

2. **Fix tie point preservation**:
   - Need to understand how tie points are associated with cameras
   - May need to transfer tie points from fisheye to pinhole cameras
   - Study `convert_to_cubemap_v012.py` for the solution

3. **Debug threading**:
   - Verify ThreadPoolExecutor is actually running multiple threads
   - Add logging to confirm parallel execution

## Updated Recommendation

The rotation fix is already working correctly. The main issues to address are:

1. **Immediate fixes**:
   - Remove incorrect sensor property assignments
   - Debug why threading isn't providing speed improvements
   
2. **Critical fixes**:
   - Solve the tie point preservation issue for COLMAP export
   - Fix the refine alignment functionality

3. **Investigation needed**:
   - Study how `convert_to_cubemap_v012.py` handles tie points
   - Understand Metashape's tie point <-> camera association