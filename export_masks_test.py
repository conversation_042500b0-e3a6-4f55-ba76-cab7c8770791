#!/usr/bin/env python3
"""
Test script to understand how to export masks from Metashape
"""

import Metashape
import os
import tempfile

def test_mask_export():
    """Test different ways to export masks"""
    
    chunk = Metashape.app.document.chunk
    if not chunk:
        print("No active chunk!")
        return
    
    print("=== MASK EXPORT TEST ===")
    
    # Find a camera with a mask
    test_camera = None
    for camera in chunk.cameras:
        if hasattr(chunk, 'masks') and chunk.masks:
            try:
                mask = chunk.masks[camera]
                if mask:
                    test_camera = camera
                    break
            except:
                pass
    
    if not test_camera:
        print("No camera with mask found!")
        return
    
    print(f"Testing with camera: {test_camera.label}")
    mask = chunk.masks[test_camera]
    print(f"Mask object: {mask}")
    print(f"Mask type: {type(mask)}")
    
    # Create temp directory
    with tempfile.TemporaryDirectory() as tmpdir:
        print(f"\nTemp directory: {tmpdir}")
        
        # Test 1: Try chunk.exportMasks if it exists
        print("\n--- Test 1: chunk.exportMasks ---")
        if hasattr(chunk, 'exportMasks'):
            try:
                output_path = os.path.join(tmpdir, "mask_test1_{filename}.png")
                chunk.exportMasks(path=output_path, cameras=[test_camera])
                print("SUCCESS: chunk.exportMasks worked")
                
                # Check what was created
                files = os.listdir(tmpdir)
                print(f"Files created: {files}")
            except Exception as e:
                print(f"FAILED: {e}")
        else:
            print("chunk.exportMasks not available")
        
        # Test 2: Try mask attributes
        print("\n--- Test 2: Mask attributes ---")
        mask_attrs = dir(mask)
        print(f"Mask attributes: {[attr for attr in mask_attrs if not attr.startswith('_')]}")
        
        # Test 3: Try to get image from mask
        print("\n--- Test 3: Get image from mask ---")
        if hasattr(mask, 'image'):
            try:
                img = mask.image()
                print(f"mask.image() returned: {img} (type: {type(img)})")
                if img and hasattr(img, 'save'):
                    test_path = os.path.join(tmpdir, "mask_test3.png")
                    img.save(test_path)
                    print(f"SUCCESS: Saved to {test_path}")
                    print(f"File size: {os.path.getsize(test_path)} bytes")
            except Exception as e:
                print(f"FAILED: {e}")
        
        # Test 4: Try photo.mask
        print("\n--- Test 4: camera.photo.mask ---")
        if test_camera.photo:
            print(f"Photo path: {test_camera.photo.path}")
            if hasattr(test_camera.photo, 'mask'):
                print(f"photo.mask: {test_camera.photo.mask}")
            
            # Check photo attributes
            photo_attrs = dir(test_camera.photo)
            print(f"Photo attributes: {[attr for attr in photo_attrs if 'mask' in attr.lower()]}")
        
        # Test 5: Try Metashape.app.document.exportMasks
        print("\n--- Test 5: Document export ---")
        if hasattr(Metashape.app.document, 'exportMasks'):
            print("document.exportMasks exists")
        else:
            print("document.exportMasks not available")
        
        # Test 6: Check if masks are stored as images
        print("\n--- Test 6: Mask as Metashape.Image ---")
        if isinstance(mask, Metashape.Image):
            print("Mask IS a Metashape.Image!")
            try:
                test_path = os.path.join(tmpdir, "mask_test6.png")
                mask.save(test_path)
                print(f"SUCCESS: Direct save worked! File size: {os.path.getsize(test_path)} bytes")
            except Exception as e:
                print(f"Direct save failed: {e}")

if __name__ == "__main__":
    test_mask_export()