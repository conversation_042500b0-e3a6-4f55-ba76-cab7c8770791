# Metashape Fisheye to Pinhole Conversion - Development History

## Overview
This document chronicles the development of a Metashape Python script to convert fisheye images to pinhole projections, replicating functionality from an existing COLMAP script.

## Initial Request
User requested a Metashape Pro script that:
- Converts circular fisheye images to 3 specific pinhole views (matching their COLMAP script)
- Generates views: `view_p45_r35`, `view_n45_r35`, `view_yL45_n54`
- Maintains camera alignment from fisheye cameras
- Uses Metashape's fisheye model
- Does NOT auto-save the project

## Key Technical Challenges

### 1. Camera-Image Mismatch Problem
**Issue**: When adding multiple photos rapidly with `chunk.addPhotos()`, Metashape doesn't guarantee order, causing wrong images to be associated with cameras.

**Failed Solutions**:
- Using `chunk.cameras[-1]` to get last added camera
- Batch adding photos then configuring
- Path comparison with normalization

**Working Solution**: 
- Process and add cameras one-by-one
- Immediately verify after each addition
- Check path matches expected

### 2. Black Output Images
**Issue**: Dewarped images were completely black.

**Cause**: Incorrect calibration parameter extraction from Metashape.

**Solution**: 
```python
fx = calib.f if hasattr(calib, 'f') else width/2
fy = fx * (1 + calib.b1 if hasattr(calib, 'b1') else 0)
cx = width/2 + (calib.cx if hasattr(calib, 'cx') else 0)
cy = height/2 + (calib.cy if hasattr(calib, 'cy') else 0)
```

### 3. OpenCV Remap Error
**Issue**: `cv2.remap` assertion error about `dst.cols < SHRT_MAX`.

**Solution**: Use `cv2.CV_32FC1` instead of `cv2.CV_16SC2` for map types.

### 4. Camera Pose/Image Direction Mismatch
**Issue**: Camera poses don't match image viewing directions (e.g., image looking down-right but pose pointing up-left).

**Root Cause**: When applying rotation to image during rectification, need to apply INVERSE rotation to camera pose.

**Attempted Fix**: 
```python
R_view_inv = R_view.T  # Transpose = inverse for rotation matrix
R_new = R_orig * R_view_inv_meta
```

**Status**: Still not working correctly - poses appear in wrong coordinate system.

### 5. Sensor Groups and Calibration
**Issue**: Cameras ending up in "unknown" sensor group with no calibration.

**Solution**: Created `fix_pinhole_cameras.py` script to:
- Find cameras with "view_" in name
- Create proper Pinhole_Back/Pinhole_Front sensors
- Set full calibration parameters
- Reassign cameras to correct sensors

## Script Evolution

### 1. `fisheye_to_perspective.py` (Initial)
- Large, feature-rich but not what was requested
- Had GUI and many options

### 2. `fisheye_to_pinhole.py` (Focused)
- Focused on 3 specific views
- Had issues with camera detection and mask loading

### 3. `fisheye_to_pinhole_one_by_one.py` (Working Version)
- **This version worked!** (with fix script after)
- Processed cameras individually
- Included test pattern verification
- Required running `fix_pinhole_cameras.py` after

### 4. `fix_pinhole_cameras.py` (Companion Script)
- Repairs unaligned cameras
- Fixes missing calibrations
- Reassigns to correct sensor groups

### 5. Various "Fixed" Versions (Regressions)
- `fisheye_to_pinhole_fixed_final.py`
- `fisheye_to_pinhole_final_fixed.py`
- `fisheye_to_pinhole_robust.py`
- All introduced new bugs while trying to fix rotation issue

### 6. `fisheye_to_pinhole_complete.py` (Combined)
- Attempted to combine working version with fix script
- Still has camera-image mismatch issues
- Rotation/pose problems persist

## Current Status

### What Works
- Image dewarping/undistortion (when correct calibration is used)
- One-by-one camera processing approach
- Sensor creation with calibration
- Fix script can repair sensor assignments

### What Doesn't Work
- Camera pose orientation (inverse rotation not working correctly)
- Reliable camera-image matching (path mismatches persist)
- Some images still get wrong calibration applied

### Core Problem
The fundamental issue appears to be that Metashape's `chunk.addPhotos()` behavior is unpredictable when adding photos rapidly. Even with one-by-one processing, the script gets path mismatches like:
```
Expected: H:/LocalSend/metashape_fisheyepinhole/5\images\fuerte_front_1072_view_n45_r35.jpg
Got: H:/LocalSend/metashape_fisheyepinhole/5/images/fuerte_front_1072_view_p45_r35.jpg
```

This causes:
1. Wrong images assigned to cameras
2. Wrong calibration used for dewarping
3. Broken sensor groups
4. Unaligned cameras

## Recommendations

1. **Use the working version**: `fisheye_to_pinhole_one_by_one.py` followed by `fix_pinhole_cameras.py`
2. **Don't try to fix rotation**: The original rotation calculation was working
3. **Consider alternative approaches**:
   - Export/import workflow instead of direct adding
   - Slower processing with delays between additions
   - Using Metashape's Python API differently

## Lessons Learned

1. Metashape's `addPhotos()` is unreliable for precise camera-image matching
2. Path normalization differs between systems (forward/backslash issues)
3. Rotation calculations for camera poses are complex - working version should not be changed
4. Sometimes "good enough" (requiring a fix script) is better than perfect
5. Test patterns were actually helpful for debugging dewarping issues