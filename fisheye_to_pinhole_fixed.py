# Fisheye to Pinhole Conversion Script for Metashape - Fixed Version
# Converts fisheye images to pinhole perspective views
# Maintains camera positions and creates proper alignment

import Metashape
import os
import sys
import math
import numpy as np
import cv2
import time

# Checking compatibility
compatible_major_version = "2.2"
found_major_version = ".".join(Metashape.app.version.split('.')[:2])
if found_major_version != compatible_major_version:
    raise Exception("Incompatible Metashape version: {} != {}".format(found_major_version, compatible_major_version))

# Default perspective views (matching COLMAP script)
DEFAULT_VIEWS = [
    {"name": "view_p45_r35", "yaw": 0.0, "pitch": 45.0, "roll": 35.3},
    {"name": "view_n45_r35", "yaw": 0.0, "pitch": -45.0, "roll": 35.3},
    {"name": "view_yL45_n54", "yaw": -45.0, "pitch": 0.0, "roll": -54.7}
]

def calculate_rotation_matrix(yaw_deg, pitch_deg, roll_deg):
    """Calculate rotation matrix from Euler angles (ZYX convention)"""
    yaw = math.radians(yaw_deg)
    pitch = math.radians(pitch_deg)
    roll = math.radians(roll_deg)
    
    # Individual rotation matrices
    Rz = np.array([
        [math.cos(yaw), -math.sin(yaw), 0],
        [math.sin(yaw), math.cos(yaw), 0],
        [0, 0, 1]
    ])
    
    Ry = np.array([
        [math.cos(pitch), 0, math.sin(pitch)],
        [0, 1, 0],
        [-math.sin(pitch), 0, math.cos(pitch)]
    ])
    
    Rx = np.array([
        [1, 0, 0],
        [0, math.cos(roll), -math.sin(roll)],
        [0, math.sin(roll), math.cos(roll)]
    ])
    
    # Combined rotation (ZYX order)
    R = Rz @ Ry @ Rx
    return R

def extract_calibration(camera):
    """Extract calibration parameters from camera"""
    sensor = camera.sensor
    if not sensor:
        return None
        
    calib = sensor.calibration
    width = sensor.width
    height = sensor.height
    
    if not calib:
        # Default calibration
        return {
            'width': width,
            'height': height,
            'fx': width / 2.0,
            'fy': width / 2.0,
            'cx': width / 2.0,
            'cy': height / 2.0,
            'k1': 0, 'k2': 0, 'k3': 0, 'k4': 0
        }
    
    # Extract parameters
    fx = calib.f if hasattr(calib, 'f') else width / 2.0
    fy = fx * (1 + calib.b1) if hasattr(calib, 'b1') else fx
    cx = width / 2.0 + (calib.cx if hasattr(calib, 'cx') else 0)
    cy = height / 2.0 + (calib.cy if hasattr(calib, 'cy') else 0)
    
    k1 = calib.k1 if hasattr(calib, 'k1') else 0
    k2 = calib.k2 if hasattr(calib, 'k2') else 0
    k3 = calib.k3 if hasattr(calib, 'k3') else 0
    k4 = calib.k4 if hasattr(calib, 'k4') else 0
    
    return {
        'width': width,
        'height': height,
        'fx': fx,
        'fy': fy,
        'cx': cx,
        'cy': cy,
        'k1': k1,
        'k2': k2,
        'k3': k3,
        'k4': k4
    }

def dewarp_fisheye_to_pinhole(image, fisheye_calib, view_rotation, output_size, fov_deg):
    """Convert fisheye image to pinhole perspective"""
    # Fisheye camera matrix
    K_fisheye = np.array([
        [fisheye_calib['fx'], 0, fisheye_calib['cx']],
        [0, fisheye_calib['fy'], fisheye_calib['cy']],
        [0, 0, 1]
    ], dtype=np.float32)
    
    # Distortion coefficients
    D_fisheye = np.array([
        fisheye_calib['k1'],
        fisheye_calib['k2'],
        fisheye_calib['k3'],
        fisheye_calib['k4']
    ], dtype=np.float32)
    
    # Pinhole camera matrix
    fov_rad = math.radians(fov_deg)
    fx = (output_size / 2.0) / math.tan(fov_rad / 2.0)
    fy = fx
    cx = output_size / 2.0
    cy = output_size / 2.0
    
    K_pinhole = np.array([
        [fx, 0, cx],
        [0, fy, cy],
        [0, 0, 1]
    ], dtype=np.float32)
    
    # Generate undistortion maps
    map1, map2 = cv2.fisheye.initUndistortRectifyMap(
        K_fisheye,
        D_fisheye,
        view_rotation.astype(np.float32),
        K_pinhole,
        (output_size, output_size),
        cv2.CV_32FC1
    )
    
    # Remap the image
    dewarped = cv2.remap(image, map1, map2, cv2.INTER_LINEAR,
                       borderMode=cv2.BORDER_CONSTANT, borderValue=(0, 0, 0))
    
    return dewarped, (fx, fy, cx, cy)

def main():
    doc = Metashape.app.document
    if not doc:
        print("No document open")
        return
        
    chunk = doc.chunk
    if not chunk:
        print("No active chunk")
        return
    
    # Get parameters
    output_dir = Metashape.app.getExistingDirectory("Select output directory for pinhole images")
    if not output_dir:
        return
        
    # Parameters
    output_size = 1920
    fov_deg = 90.0
    
    # Create output directory
    images_dir = os.path.join(output_dir, "images")
    os.makedirs(images_dir, exist_ok=True)
    
    # Find all aligned cameras
    cameras_to_process = []
    for camera in chunk.cameras:
        if camera.transform and camera.photo:
            cameras_to_process.append(camera)
    
    if not cameras_to_process:
        print("No aligned cameras found")
        return
        
    print("Found {} cameras to process".format(len(cameras_to_process)))
    
    # Group cameras by sensor
    sensor_groups = {}
    for camera in cameras_to_process:
        sensor = camera.sensor
        if sensor not in sensor_groups:
            sensor_groups[sensor] = []
        sensor_groups[sensor].append(camera)
    
    print("Found {} sensor groups".format(len(sensor_groups)))
    
    # Store new cameras info for later addition
    new_camera_info = []
    
    # Process each camera
    start_time = time.time()
    processed = 0
    
    for camera in cameras_to_process:
        print("\nProcessing: {} ({}/{})".format(camera.label, processed + 1, len(cameras_to_process)))
        
        # Get calibration
        calib = extract_calibration(camera)
        if not calib:
            print("  Skipping - no calibration")
            continue
            
        # Load image
        if not os.path.exists(camera.photo.path):
            print("  Skipping - image not found")
            continue
            
        image = cv2.imread(camera.photo.path)
        if image is None:
            print("  Skipping - failed to load image")
            continue
        
        # Get camera pose
        T = camera.transform
        
        # Process each view
        for view in DEFAULT_VIEWS:
            # Calculate view rotation
            R_view = calculate_rotation_matrix(view['yaw'], view['pitch'], view['roll'])
            
            # Dewarp image
            dewarped, (fx, fy, cx, cy) = dewarp_fisheye_to_pinhole(
                image, calib, R_view, output_size, fov_deg)
            
            # Save image
            base_name = os.path.splitext(os.path.basename(camera.photo.path))[0]
            new_name = "{}_{}".format(base_name, view['name'])
            new_path = os.path.join(images_dir, new_name + ".jpg")
            cv2.imwrite(new_path, dewarped, [cv2.IMWRITE_JPEG_QUALITY, 95])
            
            # Calculate new camera transform
            # The view looks through a rotated window of the fisheye
            # So we need to apply the rotation to the camera's coordinate system
            R_cam = Metashape.Matrix([
                [T[0,0], T[0,1], T[0,2]],
                [T[1,0], T[1,1], T[1,2]],
                [T[2,0], T[2,1], T[2,2]]
            ])
            
            R_view_meta = Metashape.Matrix([
                [R_view[0,0], R_view[0,1], R_view[0,2]],
                [R_view[1,0], R_view[1,1], R_view[1,2]],
                [R_view[2,0], R_view[2,1], R_view[2,2]]
            ])
            
            # New camera looks in direction R_cam * R_view
            R_new = R_cam * R_view_meta
            
            # Position stays the same
            pos = camera.center
            
            # Store info for later
            new_camera_info.append({
                'path': new_path,
                'label': new_name,
                'sensor_group': camera.sensor.label,
                'position': pos,
                'rotation': R_new,
                'focal': fx,
                'cx': cx - output_size/2,  # Metashape wants offset from center
                'cy': cy - output_size/2
            })
            
        processed += 1
    
    print("\nProcessed {} cameras in {:.1f} seconds".format(
        processed, time.time() - start_time))
    
    # Now add all new cameras at once
    if new_camera_info:
        print("\nAdding {} new cameras to project...".format(len(new_camera_info)))
        
        # Group by sensor
        by_sensor = {}
        for info in new_camera_info:
            sg = info['sensor_group']
            if sg not in by_sensor:
                by_sensor[sg] = []
            by_sensor[sg].append(info)
        
        # Create sensors and add cameras
        for sensor_name, cameras in by_sensor.items():
            # Create pinhole sensor
            sensor = chunk.addSensor()
            sensor.label = "Pinhole from {}".format(sensor_name)
            sensor.type = Metashape.Sensor.Type.Frame
            sensor.width = output_size
            sensor.height = output_size
            sensor.focal_length = cameras[0]['focal']
            
            # Set calibration
            calib = Metashape.Calibration()
            calib.width = output_size
            calib.height = output_size
            calib.f = cameras[0]['focal']
            calib.cx = cameras[0]['cx']
            calib.cy = cameras[0]['cy']
            calib.k1 = 0
            calib.k2 = 0
            calib.k3 = 0
            sensor.calibration = calib
            
            print("Created sensor: {}".format(sensor.label))
            
            # Add photos
            paths = [c['path'] for c in cameras]
            chunk.addPhotos(paths)
            
            # Configure each camera
            cam_start = len(chunk.cameras) - len(cameras)
            for i, info in enumerate(cameras):
                cam = chunk.cameras[cam_start + i]
                cam.label = info['label']
                cam.sensor = sensor
                
                # Set transform
                R = info['rotation']
                t = info['position']
                T = Metashape.Matrix([
                    [R[0,0], R[0,1], R[0,2], t.x],
                    [R[1,0], R[1,1], R[1,2], t.y],
                    [R[2,0], R[2,1], R[2,2], t.z],
                    [0, 0, 0, 1]
                ])
                cam.transform = T
        
        print("Successfully added all cameras")
        
        # Save project
        doc.save()
        print("Project saved")
    
    print("\nConversion complete!")
    print("Images saved to: {}".format(images_dir))

if __name__ == "__main__":
    main()