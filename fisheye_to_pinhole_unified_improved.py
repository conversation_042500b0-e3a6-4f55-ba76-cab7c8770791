#!/usr/bin/env python3
"""
Improved unified Metashape script for converting fisheye cameras to pinhole views.
Based on the working GUI version with performance improvements and additional features.
"""

import Metashape
import cv2
import numpy as np
import os
import math
import time
import sys
import traceback
from pathlib import Path
import concurrent.futures
from threading import Lock

# Version check
compatible_major_version = "2.2"
found_major_version = ".".join(Metashape.app.version.split('.')[:2])
if found_major_version != compatible_major_version:
    raise Exception("Incompatible Metashape version: {} != {}".format(found_major_version, compatible_major_version))

# Try to import PySide2
PYSIDE2_AVAILABLE = False
try:
    from PySide2 import QtCore, QtWidgets, QtGui
    PYSIDE2_AVAILABLE = True
except ImportError:
    print("Warning: PySide2 library is not available. GUI mode will not work.")

# Default views
VIEWS = [
    {"name": "view_p45_r35", "yaw": 0.0, "pitch": 45.0, "roll": 35.3},
    {"name": "view_n45_r35", "yaw": 0.0, "pitch": -45.0, "roll": 35.3},
    {"name": "view_yL45_n54", "yaw": -45.0, "pitch": 0.0, "roll": -54.7}
]

def log(msg):
    """Print timestamped log message"""
    print("[{}] {}".format(time.strftime("%H:%M:%S"), msg))

def calculate_rotation_matrix(yaw_deg, pitch_deg, roll_deg):
    """Calculate rotation matrix from Euler angles (ZYX convention)"""
    yaw = math.radians(yaw_deg)
    pitch = math.radians(pitch_deg)
    roll = math.radians(roll_deg)
    
    # Individual rotation matrices
    Rz = np.array([
        [math.cos(yaw), -math.sin(yaw), 0],
        [math.sin(yaw), math.cos(yaw), 0],
        [0, 0, 1]
    ])
    
    Ry = np.array([
        [math.cos(pitch), 0, math.sin(pitch)],
        [0, 1, 0],
        [-math.sin(pitch), 0, math.cos(pitch)]
    ])
    
    Rx = np.array([
        [1, 0, 0],
        [0, math.cos(roll), -math.sin(roll)],
        [0, math.sin(roll), math.cos(roll)]
    ])
    
    # Combined rotation (ZYX order)
    R = Rz @ Ry @ Rx
    return R

def find_or_create_sensor(chunk, sensor_name, output_size, fov_deg):
    """Find existing sensor or create new one with proper calibration"""
    # Look for existing sensor
    for sensor in chunk.sensors:
        if sensor.label == sensor_name:
            # Verify calibration is still correct
            expected_f = (output_size / 2.0) / math.tan(math.radians(fov_deg) / 2.0)
            if (sensor.calibration and 
                sensor.calibration.f and 
                abs(sensor.calibration.f - expected_f) < 1 and
                sensor.focal_length and
                abs(sensor.focal_length - expected_f) < 1):
                return sensor
    
    # Create new sensor
    sensor = chunk.addSensor()
    sensor.label = sensor_name
    sensor.type = Metashape.Sensor.Type.Frame
    sensor.width = output_size
    sensor.height = output_size
    
    # Calculate focal length for pinhole
    fov_rad = math.radians(fov_deg)
    pinhole_fx = (output_size / 2.0) / math.tan(fov_rad / 2.0)
    
    # CRITICAL: Set the focal length in mm (Metashape uses this as primary reference)
    # For a virtual sensor, we use pixel dimensions of 1mm x 1mm
    # so focal length in mm equals focal length in pixels
    sensor.focal_length = pinhole_fx
    sensor.pixel_width = 1.0   # 1mm per pixel
    sensor.pixel_height = 1.0  # 1mm per pixel
    
    # Create new calibration object instead of modifying existing
    calib = Metashape.Calibration()
    calib.type = Metashape.Sensor.Type.Frame
    calib.width = output_size
    calib.height = output_size
    calib.f = pinhole_fx  # Focal length in pixels
    calib.cx = 0  # Principal point offset from center
    calib.cy = 0
    calib.b1 = 0  # No aspect ratio difference
    calib.b2 = 0
    # Set distortion coefficients to 0 for pinhole
    calib.k1 = 0
    calib.k2 = 0
    calib.k3 = 0
    calib.k4 = 0
    calib.p1 = 0
    calib.p2 = 0
    sensor.calibration = calib
    
    # Fix calibration parameters
    sensor.fixed = True  # Fix calibration
    
    # Log the focal length for debugging
    log("Created sensor '{}' with focal length: {:.2f} pixels (FOV: {:.1f} deg)".format(
        sensor_name, pinhole_fx, fov_deg))
    log("  sensor.focal_length = {:.2f} mm".format(sensor.focal_length))
    log("  sensor.calibration.f = {:.2f} pixels".format(sensor.calibration.f))
    log("  sensor.pixel_width = {:.2f} mm".format(sensor.pixel_width))
    log("  sensor.pixel_height = {:.2f} mm".format(sensor.pixel_height))
    
    return sensor

def process_image_for_view(camera_data, view, output_dir, output_size, fov_deg):
    """Process a single view for a camera (can be run in parallel)"""
    try:
        # Extract camera data
        image_path = camera_data['image_path']
        fx = camera_data['fx']
        fy = camera_data['fy']
        cx = camera_data['cx']
        cy = camera_data['cy']
        k1 = camera_data['k1']
        k2 = camera_data['k2']
        k3 = camera_data['k3']
        k4 = camera_data['k4']
        base_name = camera_data['base_name']
        mask_data = camera_data.get('mask_data')
        
        # Calculate rotation
        R_view = calculate_rotation_matrix(view['yaw'], view['pitch'], view['roll'])
        
        # Setup matrices
        K_fisheye = np.array([
            [fx, 0, cx],
            [0, fy, cy],
            [0, 0, 1]
        ], dtype=np.float32)
        
        D_fisheye = np.array([k1, k2, k3, k4], dtype=np.float32)
        
        fov_rad = math.radians(fov_deg)
        pinhole_f = (output_size / 2.0) / math.tan(fov_rad / 2.0)
        K_pinhole = np.array([
            [pinhole_f, 0, output_size/2],
            [0, pinhole_f, output_size/2],
            [0, 0, 1]
        ], dtype=np.float32)
        
        # Generate undistortion maps (use CV_16SC2 for faster remapping)
        map1, map2 = cv2.fisheye.initUndistortRectifyMap(
            K_fisheye,
            D_fisheye,
            R_view.astype(np.float32),
            K_pinhole,
            (output_size, output_size),
            cv2.CV_16SC2  # Changed from CV_32FC1 for faster processing
        )
        
        # Load and process image
        image = cv2.imread(image_path)
        if image is None:
            raise Exception("Failed to load image: {}".format(image_path))
        
        # Apply remapping
        dewarped = cv2.remap(image, map1, map2, cv2.INTER_LINEAR,
                           borderMode=cv2.BORDER_CONSTANT, borderValue=(0, 0, 0))
        
        # Save image
        new_name = "{}_{}".format(base_name, view['name'])
        new_path = os.path.join(output_dir, "images", new_name + ".jpg")
        
        # Create directory if needed
        os.makedirs(os.path.dirname(new_path), exist_ok=True)
        
        cv2.imwrite(new_path, dewarped, [cv2.IMWRITE_JPEG_QUALITY, 95])
        
        # Process mask if available from Metashape
        mask_new_path = None
        if mask_data and mask_data.get('has_mask') and mask_data.get('temp_mask_path'):
            try:
                # Load mask
                mask = cv2.imread(mask_data['temp_mask_path'], cv2.IMREAD_GRAYSCALE)
                if mask is not None:
                    # Apply same remapping to mask
                    dewarped_mask = cv2.remap(mask, map1, map2, cv2.INTER_NEAREST,
                                            borderMode=cv2.BORDER_CONSTANT, borderValue=0)
                    
                    # Threshold to ensure binary mask
                    _, dewarped_mask_binary = cv2.threshold(dewarped_mask, 127, 255, cv2.THRESH_BINARY)
                    
                    # Save processed mask
                    mask_new_name = "{}_{}".format(base_name, view['name'])
                    mask_new_path = os.path.join(output_dir, "masks", mask_new_name + ".png")
                    os.makedirs(os.path.dirname(mask_new_path), exist_ok=True)
                    cv2.imwrite(mask_new_path, dewarped_mask_binary)
                    
            except Exception as mask_error:
                log("Failed to process mask for {}: {}".format(base_name, str(mask_error)))
        
        return {
            'success': True,
            'view_name': view['name'],
            'new_path': new_path,
            'new_name': new_name,
            'mask_path': mask_new_path,
            'R_view': R_view
        }
        
    except Exception as e:
        return {
            'success': False,
            'view_name': view['name'],
            'error': str(e)
        }

def add_camera_with_retry(chunk, image_path, max_retries=10):
    """Add camera with retry logic to handle async issues"""
    cameras_before = list(chunk.cameras)
    chunk.addPhotos([image_path])
    
    # Wait for the camera to be added
    for retry in range(max_retries):
        time.sleep(0.2)  # Wait 200ms
        
        # Find new cameras
        cameras_after = list(chunk.cameras)
        new_cameras = [c for c in cameras_after if c not in cameras_before]
        
        if new_cameras:
            # Check if any of the new cameras match our image
            for new_cam in new_cameras:
                if new_cam.photo and os.path.normpath(new_cam.photo.path) == os.path.normpath(image_path):
                    return new_cam
    
    # If we get here, we failed to find the camera
    return None

def check_if_views_exist(camera, output_dir, selected_views):
    """Check if all views for a camera already exist"""
    base_name = os.path.splitext(os.path.basename(camera.photo.path))[0]
    
    for view in VIEWS:
        if view['name'] not in selected_views:
            continue
        
        new_name = "{}_{}".format(base_name, view['name'])
        new_path = os.path.join(output_dir, "images", new_name + ".jpg")
        
        if not os.path.exists(new_path):
            return False
    
    return True

def process_single_camera(camera, output_dir, output_size, fov_deg, chunk, sensor_cache, group_cache, selected_views, 
                         skip_existing, thread_count=4, process_masks=True):
    """Process a single camera and add its views to the chunk"""
    # Check if we should skip existing
    if skip_existing and check_if_views_exist(camera, output_dir, selected_views):
        log("Skipping {} - views already exist".format(camera.label))
        return [], True  # Return empty list and success flag
    
    # Get calibration
    sensor = camera.sensor
    if not sensor:
        raise Exception("No sensor")
    
    calib = sensor.calibration
    if not calib:
        raise Exception("No calibration")
    
    # Extract calibration parameters
    width = sensor.width
    height = sensor.height
    
    # Get focal length and principal point
    fx = calib.f if hasattr(calib, 'f') else width/2
    fy = fx * (1 + calib.b1 if hasattr(calib, 'b1') else 0)
    cx = width/2 + (calib.cx if hasattr(calib, 'cx') else 0)
    cy = height/2 + (calib.cy if hasattr(calib, 'cy') else 0)
    
    # Get distortion
    k1 = calib.k1 if hasattr(calib, 'k1') else 0
    k2 = calib.k2 if hasattr(calib, 'k2') else 0
    k3 = calib.k3 if hasattr(calib, 'k3') else 0
    k4 = calib.k4 if hasattr(calib, 'k4') else 0
    
    # Check if image exists
    if not os.path.exists(camera.photo.path):
        raise FileNotFoundError("Image not found: {}".format(camera.photo.path))
    
    # Get mask from Metashape camera if it exists and mask processing is enabled
    mask_data = None
    
    # COMPREHENSIVE MASK DEBUGGING
    log("=== MASK DEBUG INFO FOR CAMERA: {} ===".format(camera.label))
    
    # First check if masks are stored at the chunk level
    if hasattr(chunk, 'masks'):
        log("Chunk has masks attribute: {} (type: {})".format(chunk.masks, type(chunk.masks)))
        # Don't try to get len() of Metashape.Masks object
    
    # Check if masks might be in a mask group
    if hasattr(chunk, 'mask_group'):
        log("Chunk has mask_group: {}".format(chunk.mask_group))
    
    # Check camera's photo for mask info
    if camera.photo:
        log("Camera has photo: {}".format(camera.photo.path))
        if hasattr(camera.photo, 'mask'):
            log("Photo has mask attribute: {}".format(camera.photo.mask))
        if hasattr(camera.photo, 'masks'):
            log("Photo has masks attribute: {}".format(camera.photo.masks))
    
    # Check different ways masks might be stored
    log("Checking camera.mask...")
    log("Process masks enabled: {}".format(process_masks))
    
    # Check if mask exists in chunk.masks for this camera
    mask_from_chunk = None
    if hasattr(chunk, 'masks') and chunk.masks:
        try:
            mask_from_chunk = chunk.masks[camera]
            log("Found mask in chunk.masks for camera: {}".format(type(mask_from_chunk)))
        except:
            log("No mask found in chunk.masks for this camera")
    
    # Check if camera.mask exists and what type it is
    mask_exists = (mask_from_chunk is not None) or (hasattr(camera, 'mask') and camera.mask is not None)
    log("Mask exists: {} (from chunk: {}, from camera: {})".format(
        mask_exists, 
        mask_from_chunk is not None,
        hasattr(camera, 'mask') and camera.mask is not None
    ))
    
    if hasattr(camera, 'mask'):
        log("camera.mask attribute present: {}".format(camera.mask))
        log("camera.mask type: {}".format(type(camera.mask)))
        
        if camera.mask is not None:
            # Try to get mask properties
            try:
                # Check if mask has standard properties
                if hasattr(camera.mask, '__class__'):
                    log("Mask class: {}".format(camera.mask.__class__))
                if hasattr(camera.mask, '__dict__'):
                    log("Mask attributes: {}".format(list(camera.mask.__dict__.keys()) if camera.mask.__dict__ else "No dict"))
                
                # Try to call methods that might exist
                try:
                    # Some masks might have size info
                    if hasattr(camera.mask, 'size'):
                        log("Mask size: {}".format(camera.mask.size))
                    if hasattr(camera.mask, 'width') and hasattr(camera.mask, 'height'):
                        log("Mask dimensions: {}x{}".format(camera.mask.width, camera.mask.height))
                except Exception as prop_e:
                    log("Error getting mask properties: {}".format(str(prop_e)))
                    
            except Exception as info_e:
                log("Error inspecting mask object: {}".format(str(info_e)))
    else:
        log("camera.mask attribute not present")
    
    if process_masks and mask_exists:
        # Camera has a mask applied in Metashape - save it to a temp file for threading
        import tempfile
        try:
            # Create temporary file for the mask
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                temp_mask_path = tmp_file.name
            
            # Get the mask object
            mask_obj = mask_from_chunk if mask_from_chunk else camera.mask
            
            # Get mask image data
            try:
                # Based on the test script output, we know masks have an image() method
                if hasattr(mask_obj, 'image'):
                    mask_image = mask_obj.image()
                    if mask_image:
                        mask_image.save(temp_mask_path)
                    else:
                        raise Exception("mask.image() returned None")
                else:
                    raise Exception("Mask object missing image() method")
                    
            except Exception as e:
                log("Failed to save mask: {}".format(str(e)))
                # Since we can't save the mask, we'll skip mask processing for this camera
                mask_data = {'has_mask': False, 'temp_mask_path': None}
                log("Skipping mask processing due to save failure")
            
            # Verify the file was created and has content
            if os.path.exists(temp_mask_path) and os.path.getsize(temp_mask_path) > 0:
                mask_data = {'has_mask': True, 'temp_mask_path': temp_mask_path}
            else:
                mask_data = {'has_mask': False, 'temp_mask_path': None}
                    
        except Exception as e:
            log("Failed to save mask for {}: {}".format(camera.label, str(e)))
            mask_data = {'has_mask': False, 'temp_mask_path': None}
    else:
        mask_data = {'has_mask': False, 'temp_mask_path': None}
    
    # Prepare camera data for parallel processing
    camera_data = {
        'image_path': camera.photo.path,
        'fx': fx,
        'fy': fy,
        'cx': cx,
        'cy': cy,
        'k1': k1,
        'k2': k2,
        'k3': k3,
        'k4': k4,
        'base_name': os.path.splitext(os.path.basename(camera.photo.path))[0],
        'mask_data': mask_data
    }
    
    # Determine sensor name based on camera label
    if "back" in camera.label.lower():
        sensor_name = "Pinhole_Back"
    elif "front" in camera.label.lower():
        sensor_name = "Pinhole_Front"
    else:
        sensor_name = "Pinhole_Unknown"
    
    # Get or create sensor
    if sensor_name not in sensor_cache:
        sensor_cache[sensor_name] = find_or_create_sensor(chunk, sensor_name, output_size, fov_deg)
    
    pinhole_sensor = sensor_cache[sensor_name]
    
    # Process views
    views_to_process = [v for v in VIEWS if v['name'] in selected_views]
    results = []
    
    if thread_count > 1 and len(views_to_process) > 1:
        # Use ThreadPoolExecutor for parallel image processing
        thread_start_time = time.time()
        actual_workers = min(thread_count, len(views_to_process))
        log("Starting parallel processing with {} threads for {} views".format(actual_workers, len(views_to_process)))
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=actual_workers) as executor:
            futures = []
            for view in views_to_process:
                future = executor.submit(process_image_for_view, camera_data, view, output_dir, output_size, fov_deg)
                futures.append(future)
            
            # Collect results
            for future in concurrent.futures.as_completed(futures):
                result = future.result()
                results.append(result)
        
        thread_time = time.time() - thread_start_time
        log("Parallel processing completed in {:.2f} seconds".format(thread_time))
    else:
        # Sequential processing (may be faster for small numbers of views)
        log("Processing {} views sequentially".format(len(views_to_process)))
        for view in views_to_process:
            result = process_image_for_view(camera_data, view, output_dir, output_size, fov_deg)
            results.append(result)
    
    # Check for errors
    errors = [r for r in results if not r['success']]
    if errors:
        error_msgs = ["{}: {}".format(e['view_name'], e['error']) for e in errors]
        raise Exception("Failed to process views: " + "; ".join(error_msgs))
    
    # Add cameras to Metashape (must be done sequentially)
    added_cameras = []
    
    for result in results:
        if not result['success']:
            continue
        
        # Add camera with retry logic
        new_cam = add_camera_with_retry(chunk, result['new_path'])
        
        if not new_cam:
            raise Exception("Failed to add camera for {}".format(result['new_name']))
        
        # Configure camera
        new_cam.label = result['new_name']
        new_cam.sensor = pinhole_sensor
        new_cam.enabled = True  # Ensure camera is enabled
        
        # Ensure the sensor type is properly set (critical for COLMAP export)
        if new_cam.sensor.type != Metashape.Sensor.Type.Frame:
            log("WARNING: Sensor type mismatch for {}, fixing...".format(result['new_name']))
            new_cam.sensor.type = Metashape.Sensor.Type.Frame
        
        # Assign to camera group
        group_name = sensor_name.replace("Pinhole_", "pinhole_")
        if group_name not in group_cache:
            # Find or create camera group
            existing_group = None
            for group in chunk.camera_groups:
                if group.label == group_name:
                    existing_group = group
                    break
            if not existing_group:
                existing_group = chunk.addCameraGroup()
                existing_group.label = group_name
            group_cache[group_name] = existing_group
        new_cam.group = group_cache[group_name]
        
        # Verify focal length was set correctly
        if new_cam.sensor and new_cam.sensor.calibration:
            actual_f = new_cam.sensor.calibration.f
            expected_f = (output_size / 2.0) / math.tan(math.radians(fov_deg) / 2.0)
            if abs(actual_f - expected_f) > 1:
                log("WARNING: Focal length mismatch for {}: expected {:.2f}, got {:.2f}".format(
                    result['new_name'], expected_f, actual_f))
            
            # Also verify sensor focal length
            if new_cam.sensor.focal_length:
                actual_sensor_f = new_cam.sensor.focal_length
                if abs(actual_sensor_f - expected_f) > 1:
                    log("WARNING: Sensor focal length mismatch for {}: expected {:.2f}, got {:.2f}".format(
                        result['new_name'], expected_f, actual_sensor_f))
            else:
                log("WARNING: Sensor focal length not set for {}".format(result['new_name']))
        
        # Apply mask if it was processed
        log("=== MASK APPLICATION DEBUG FOR: {} ===".format(result['new_name']))
        mask_path = result.get('mask_path')
        log("Mask path from result: {}".format(mask_path))
        
        if mask_path:
            log("Checking if mask file exists: {}".format(mask_path))
            mask_exists = os.path.exists(mask_path)
            log("Mask file exists: {}".format(mask_exists))
            
            if mask_exists:
                try:
                    log("File size: {} bytes".format(os.path.getsize(mask_path)))
                    log("Attempting to load mask...")
                    
                    # Load and apply the processed mask to the new camera
                    mask = Metashape.Mask()
                    log("Created Metashape.Mask() object: {}".format(type(mask)))
                    
                    log("Calling mask.load() with path: {}".format(mask_path))
                    mask.load(mask_path)
                    log("Mask loaded successfully")
                    
                    log("Applying mask to camera...")
                    new_cam.mask = mask
                    log("SUCCESS: Applied processed mask to camera: {}".format(result['new_name']))
                    
                    # Verify the mask was applied
                    if new_cam.mask:
                        log("Verification: Camera now has mask: {}".format(type(new_cam.mask)))
                    else:
                        log("WARNING: Camera mask is None after assignment")
                        
                except Exception as mask_error:
                    log("ERROR: Failed to apply mask to {}: {}".format(result['new_name'], str(mask_error)))
                    log("Error type: {}".format(type(mask_error)))
                    import traceback
                    log("Full traceback:")
                    log(traceback.format_exc())
            else:
                log("Mask file does not exist, skipping mask application")
        else:
            log("No mask path provided, skipping mask application")
        log("=== END MASK APPLICATION DEBUG ===")
        log("")  # Empty line for readability
        
        # Calculate transform if original camera is aligned
        if camera.transform:
            T_orig = camera.transform
            R_orig = Metashape.Matrix([
                [T_orig[0,0], T_orig[0,1], T_orig[0,2]],
                [T_orig[1,0], T_orig[1,1], T_orig[1,2]],
                [T_orig[2,0], T_orig[2,1], T_orig[2,2]]
            ])
            
            # When we rotate the image by R_view, we need to rotate the camera pose by R_view^T (inverse)
            R_view = result['R_view']
            R_view_inv = R_view.T  # Transpose = inverse for rotation matrices
            
            R_view_inv_meta = Metashape.Matrix([
                [R_view_inv[0,0], R_view_inv[0,1], R_view_inv[0,2]],
                [R_view_inv[1,0], R_view_inv[1,1], R_view_inv[1,2]],
                [R_view_inv[2,0], R_view_inv[2,1], R_view_inv[2,2]]
            ])
            
            # New rotation
            R_new = R_orig * R_view_inv_meta
            
            # Same position
            pos = camera.center
            
            # Build transform
            T_new = Metashape.Matrix([
                [R_new[0,0], R_new[0,1], R_new[0,2], pos.x],
                [R_new[1,0], R_new[1,1], R_new[1,2], pos.y],
                [R_new[2,0], R_new[2,1], R_new[2,2], pos.z],
                [0, 0, 0, 1]
            ])
            
            new_cam.transform = T_new
        
        added_cameras.append(new_cam)
    
    # Clean up temporary mask file
    if mask_data and mask_data.get('temp_mask_path'):
        try:
            os.unlink(mask_data['temp_mask_path'])
        except:
            pass
    
    return added_cameras, False  # Return cameras and skip flag

# GUI Classes
if PYSIDE2_AVAILABLE:
    class ProcessCamerasThread(QtCore.QThread):
        """Thread for processing cameras without blocking GUI"""
        update_progress = QtCore.Signal(int, str)
        processing_finished = QtCore.Signal(bool, dict)
        
        def __init__(self, cameras_to_process, options):
            super().__init__()
            self.cameras_to_process = cameras_to_process
            self.options = options
            self._is_stopped = False
            self.lock = Lock()
            
        def stop(self):
            self._is_stopped = True
            
        def run(self):
            start_time = time.time()
            total_cameras = len(self.cameras_to_process)
            processed_count = 0
            skipped_count = 0
            skipped_existing = 0
            errors = []
            new_cameras_added = []
            
            chunk = Metashape.app.document.chunk
            sensor_cache = {}
            group_cache = {}
            
            # Create output directories
            os.makedirs(self.options['images_dir'], exist_ok=True)
            if self.options['process_masks']:
                masks_dir = os.path.join(self.options['output_folder'], 'masks')
                os.makedirs(masks_dir, exist_ok=True)
                log("Created masks directory: {}".format(masks_dir))
            
            for i, camera in enumerate(self.cameras_to_process):
                if self._is_stopped:
                    break
                    
                progress = int((i / total_cameras) * 100)
                self.update_progress.emit(progress, "Processing camera {} ({}/{})...".format(
                    camera.label, i+1, total_cameras))
                
                try:
                    added_cameras, was_skipped = process_single_camera(
                        camera, 
                        self.options['output_folder'],
                        self.options['output_size'],
                        self.options['fov_deg'],
                        chunk,
                        sensor_cache,
                        group_cache,
                        self.options['selected_views'],
                        self.options['skip_existing'],
                        self.options['thread_count'],
                        self.options['process_masks']
                    )
                    
                    if was_skipped:
                        skipped_existing += 1
                    else:
                        new_cameras_added.extend(added_cameras)
                        processed_count += 1
                        
                except Exception as e:
                    skipped_count += 1
                    error_msg = "Failed to process {}: {}".format(camera.label, str(e))
                    errors.append(error_msg)
                    log(error_msg)
                    
            total_time = time.time() - start_time
            
            if self._is_stopped:
                self.processing_finished.emit(False, {"message": "Processing was stopped by user."})
            else:
                self.processing_finished.emit(True, {
                    "processed": processed_count,
                    "skipped": skipped_count,
                    "skipped_existing": skipped_existing,
                    "total": total_cameras,
                    "time": total_time,
                    "errors": errors,
                    "new_cameras": new_cameras_added
                })
    
    class FisheyeConverterGUI(QtWidgets.QDialog):
        """Main GUI window"""
        def __init__(self):
            super().__init__()
            self.process_thread = None
            self.removal_dialog_shown = False  # Guard to prevent multiple dialogs
            self.init_ui()
            
        def init_ui(self):
            self.setWindowTitle("Fisheye to Pinhole Converter (Improved)")
            self.setModal(True)
            self.resize(650, 600)
            
            layout = QtWidgets.QVBoxLayout()
            
            # Settings group
            settings_group = QtWidgets.QGroupBox("Settings")
            settings_layout = QtWidgets.QGridLayout()
            
            # Output folder
            settings_layout.addWidget(QtWidgets.QLabel("Output Folder:"), 0, 0)
            self.output_folder_edit = QtWidgets.QLineEdit()
            self.output_folder_edit.setReadOnly(True)
            settings_layout.addWidget(self.output_folder_edit, 0, 1)
            browse_btn = QtWidgets.QPushButton("Browse...")
            browse_btn.clicked.connect(self.browse_output_folder)
            settings_layout.addWidget(browse_btn, 0, 2)
            
            # Output size
            settings_layout.addWidget(QtWidgets.QLabel("Output Size (px):"), 1, 0)
            self.size_spin = QtWidgets.QSpinBox()
            self.size_spin.setRange(256, 8192)
            self.size_spin.setValue(1920)
            self.size_spin.setSingleStep(128)
            settings_layout.addWidget(self.size_spin, 1, 1)
            
            # FOV
            settings_layout.addWidget(QtWidgets.QLabel("Field of View (deg):"), 2, 0)
            self.fov_spin = QtWidgets.QDoubleSpinBox()
            self.fov_spin.setRange(30.0, 120.0)
            self.fov_spin.setValue(90.0)
            self.fov_spin.setSingleStep(5.0)
            settings_layout.addWidget(self.fov_spin, 2, 1)
            
            # Thread count
            settings_layout.addWidget(QtWidgets.QLabel("Processing Threads:"), 3, 0)
            self.thread_spin = QtWidgets.QSpinBox()
            self.thread_spin.setRange(1, 16)
            self.thread_spin.setValue(4)
            self.thread_spin.setToolTip("Number of threads for parallel view processing per camera")
            settings_layout.addWidget(self.thread_spin, 3, 1)
            
            settings_group.setLayout(settings_layout)
            layout.addWidget(settings_group)
            
            # Views group
            views_group = QtWidgets.QGroupBox("Views to Generate")
            views_layout = QtWidgets.QVBoxLayout()
            
            self.view_checkboxes = {}
            for view in VIEWS:
                cb = QtWidgets.QCheckBox("{} (Yaw:{}, Pitch:{}, Roll:{})".format(
                    view['name'], view['yaw'], view['pitch'], view['roll']))
                cb.setChecked(True)
                self.view_checkboxes[view['name']] = cb
                views_layout.addWidget(cb)
            
            views_group.setLayout(views_layout)
            layout.addWidget(views_group)
            
            # Options group
            options_group = QtWidgets.QGroupBox("Options")
            options_layout = QtWidgets.QVBoxLayout()
            
            self.skip_existing_cb = QtWidgets.QCheckBox("Skip processing if image files already exist")
            self.skip_existing_cb.setToolTip("Only add and align cameras for existing images, don't regenerate them")
            options_layout.addWidget(self.skip_existing_cb)
            
            self.disable_originals_cb = QtWidgets.QCheckBox("Disable original fisheye cameras after processing")
            self.disable_originals_cb.setChecked(True)
            self.disable_originals_cb.setToolTip("Keeps tie points but excludes fisheye cameras from processing")
            options_layout.addWidget(self.disable_originals_cb)
            
            self.delete_originals_cb = QtWidgets.QCheckBox("Delete original fisheye cameras after processing")
            self.delete_originals_cb.setToolTip("Removes fisheye cameras after processing.\n" +
                                                "WARNING: This will remove tie points detected on fisheye images.\n" +
                                                "You'll need to run 'Align Photos' again for pinhole views.")
            options_layout.addWidget(self.delete_originals_cb)
            
            self.realign_after_delete_cb = QtWidgets.QCheckBox("Create tie points for pinhole cameras after deleting fisheyes")
            self.realign_after_delete_cb.setToolTip("Runs Match Photos on pinhole cameras after removing fisheyes.\n" +
                                                    "This creates tie points while preserving the camera poses from fisheye alignment.\n" +
                                                    "Tie points are needed for some export formats like COLMAP.")
            self.realign_after_delete_cb.setEnabled(False)  # Only enabled when delete is checked
            self.realign_after_delete_cb.setChecked(True)  # Default to enabled when available
            options_layout.addWidget(self.realign_after_delete_cb)
            
            # Make disable and delete mutually exclusive
            self.disable_originals_cb.toggled.connect(lambda checked: self.delete_originals_cb.setEnabled(not checked) if checked else None)
            self.delete_originals_cb.toggled.connect(lambda checked: (
                self.disable_originals_cb.setEnabled(not checked) if checked else None,
                self.realign_after_delete_cb.setEnabled(checked)
            )[0])
            
            self.refine_alignment_cb = QtWidgets.QCheckBox("Optimize positions of new cameras")
            self.refine_alignment_cb.setToolTip("Fine-tune the positions of new pinhole cameras using existing tie points.\n" +
                                                "WARNING: This requires tie points to work properly.\n" +
                                                "Without tie points, camera poses may be lost!")
            self.refine_alignment_cb.setChecked(False)  # Default to disabled - new cameras inherit transforms
            options_layout.addWidget(self.refine_alignment_cb)
            
            self.process_masks_cb = QtWidgets.QCheckBox("Process and apply masks from fisheye cameras")
            self.process_masks_cb.setToolTip("Transform masks from fisheye cameras and apply to pinhole views")
            self.process_masks_cb.setChecked(True)
            options_layout.addWidget(self.process_masks_cb)
            
            options_group.setLayout(options_layout)
            layout.addWidget(options_group)
            
            # Progress group
            progress_group = QtWidgets.QGroupBox("Progress")
            progress_layout = QtWidgets.QVBoxLayout()
            
            self.status_label = QtWidgets.QLabel("Ready")
            progress_layout.addWidget(self.status_label)
            
            self.progress_bar = QtWidgets.QProgressBar()
            progress_layout.addWidget(self.progress_bar)
            
            progress_group.setLayout(progress_layout)
            layout.addWidget(progress_group)
            
            # Buttons
            button_layout = QtWidgets.QHBoxLayout()
            button_layout.addStretch()
            
            self.start_button = QtWidgets.QPushButton("Start")
            self.start_button.clicked.connect(self.start_processing)
            button_layout.addWidget(self.start_button)
            
            self.stop_button = QtWidgets.QPushButton("Stop")
            self.stop_button.clicked.connect(self.stop_processing)
            self.stop_button.setEnabled(False)
            button_layout.addWidget(self.stop_button)
            
            self.close_button = QtWidgets.QPushButton("Close")
            self.close_button.clicked.connect(self.close)
            button_layout.addWidget(self.close_button)
            
            layout.addLayout(button_layout)
            self.setLayout(layout)
            
        def browse_output_folder(self):
            folder = QtWidgets.QFileDialog.getExistingDirectory(
                self, "Select Output Folder")
            if folder:
                self.output_folder_edit.setText(folder)
                
        def start_processing(self):
            # Validate inputs
            if not self.output_folder_edit.text():
                QtWidgets.QMessageBox.warning(self, "Error", "Please select an output folder.")
                return
                
            chunk = Metashape.app.document.chunk
            if not chunk:
                QtWidgets.QMessageBox.warning(self, "Error", "No active chunk.")
                return
                
            # Find cameras to process (original cameras that aren't already converted)
            cameras_to_process = []
            cameras_with_masks = 0
            camera_mask_details = []
            
            log("=== SCANNING CAMERAS FOR MASKS ===")
            for camera in chunk.cameras:
                if (camera.sensor and 
                    camera.transform and 
                    camera.photo and
                    "view_" not in camera.label):  # Process any aligned original camera
                    
                    # Check if it has fisheye calibration parameters (k1, k2, k3, k4)
                    calib = camera.sensor.calibration
                    if calib and (hasattr(calib, 'k1') or hasattr(calib, 'k2') or 
                                  hasattr(calib, 'k3') or hasattr(calib, 'k4')):
                        cameras_to_process.append(camera)
                        
                        # Check for mask in chunk.masks (the correct location in Metashape 2.x)
                        has_mask = False
                        if hasattr(chunk, 'masks') and chunk.masks:
                            try:
                                mask = chunk.masks[camera]
                                if mask:
                                    has_mask = True
                                    cameras_with_masks += 1
                                    camera_mask_details.append("{} (from chunk.masks)".format(camera.label))
                            except:
                                pass
                        
                        # Also check camera.mask as fallback
                        if not has_mask and hasattr(camera, 'mask') and camera.mask is not None:
                            has_mask = True
                            cameras_with_masks += 1
                            camera_mask_details.append("{} (from camera.mask)".format(camera.label))
            
            log("=== MASK SCAN COMPLETE ===")
            log("Found {} cameras to process, {} have masks".format(len(cameras_to_process), cameras_with_masks))
            
            if cameras_with_masks > 0:
                log("Cameras with masks:")
                for detail in camera_mask_details:
                    log("  - {}".format(detail))
            else:
                log("No cameras have masks applied")
            log("")  # Empty line
                    
            if not cameras_to_process:
                QtWidgets.QMessageBox.warning(self, "Error", 
                    "No aligned cameras with fisheye calibration found to process.\n\n"
                    "Make sure:\n"
                    "1. Cameras are aligned (have positions)\n"
                    "2. Cameras have fisheye distortion parameters (k1-k4)\n"
                    "3. Cameras haven't already been converted (no 'view_' prefix)")
                return
                
            # Get selected views
            selected_views = [name for name, cb in self.view_checkboxes.items() if cb.isChecked()]
            if not selected_views:
                QtWidgets.QMessageBox.warning(self, "Error", "Please select at least one view.")
                return
                
            # Prepare options
            options = {
                'output_folder': self.output_folder_edit.text(),
                'images_dir': os.path.join(self.output_folder_edit.text(), 'images'),
                'output_size': self.size_spin.value(),
                'fov_deg': self.fov_spin.value(),
                'selected_views': selected_views,
                'skip_existing': self.skip_existing_cb.isChecked(),
                'thread_count': self.thread_spin.value(),
                'process_masks': self.process_masks_cb.isChecked()
            }
            
            # Start processing
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.close_button.setEnabled(False)
            self.progress_bar.setValue(0)
            
            self.process_thread = ProcessCamerasThread(cameras_to_process, options)
            self.process_thread.update_progress.connect(self.on_update_progress)
            self.process_thread.processing_finished.connect(self.on_processing_finished)
            self.process_thread.start()
            
        def stop_processing(self):
            if self.process_thread:
                self.process_thread.stop()
                self.status_label.setText("Stopping...")
                self.stop_button.setEnabled(False)
                
        def on_update_progress(self, progress, status):
            self.progress_bar.setValue(progress)
            self.status_label.setText(status)
            
        def on_processing_finished(self, success, stats):
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.close_button.setEnabled(True)
            self.removal_dialog_shown = False  # Reset the flag for next run
            
            if not success:
                QtWidgets.QMessageBox.warning(self, "Aborted", stats["message"])
                self.status_label.setText("Aborted")
                return
                
            # Post-processing
            chunk = Metashape.app.document.chunk
            
            # Track if fisheye removal was cancelled
            removal_cancelled = False
            
            if self.disable_originals_cb.isChecked():
                self.status_label.setText("Disabling original cameras...")
                QtWidgets.QApplication.processEvents()
                
                disabled_count = 0
                for camera in chunk.cameras:
                    # Disable original cameras (those without "view_" prefix)
                    if camera.sensor and "view_" not in camera.label:
                        camera.enabled = False
                        disabled_count += 1
            
            elif self.delete_originals_cb.isChecked():
                self.status_label.setText("Preparing to remove original cameras...")
                QtWidgets.QApplication.processEvents()
                
                # New strategy: Don't try to create new tie points between fisheye and pinhole cameras
                # as they have very different projections. Instead:
                # 1. The pinhole cameras already have inherited transforms from fisheye cameras
                # 2. We'll remove the fisheye cameras
                # 3. User can run Match Photos manually after if needed
                
                # Get fisheye cameras to remove (original cameras without "view_" prefix)
                fisheye_cameras = [cam for cam in chunk.cameras 
                                 if cam.sensor and "view_" not in cam.label]
                
                # Count pinhole cameras that will remain (generated cameras with "view_" prefix)
                pinhole_cameras = [cam for cam in chunk.cameras 
                                 if cam.sensor and "view_" in cam.label]
                
                log("Preparing to remove {} fisheye cameras, keeping {} pinhole cameras".format(
                    len(fisheye_cameras), len(pinhole_cameras)))
                
                # Show warning to user about tie points
                if fisheye_cameras and pinhole_cameras and not self.removal_dialog_shown:
                    self.removal_dialog_shown = True  # Set flag to prevent multiple dialogs
                    log("Showing removal confirmation dialog...")
                    msg = ("Removing {} original cameras will remove all tie points since they were "
                           "detected on the fisheye images.\n\n"
                           "The {} pinhole cameras will keep their positions from the fisheye alignment.\n\n"
                           "New tie points will need to be created between the pinhole views.\n\n"
                           "Continue with removal?").format(len(fisheye_cameras), len(pinhole_cameras))
                    
                    # Create dialog in a way that prevents event loop issues
                    reply = QtWidgets.QMessageBox.question(
                        None,  # Use None as parent to avoid event loop issues
                        "Tie Points Warning", 
                        msg,
                        QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
                        QtWidgets.QMessageBox.No
                    )
                    log("User response: {}".format("Yes" if reply == QtWidgets.QMessageBox.Yes else "No"))
                    
                    if reply != QtWidgets.QMessageBox.Yes:
                        log("User cancelled fisheye camera removal")
                        self.status_label.setText("Removal cancelled by user")
                        # Skip the removal but continue with other processing
                        fisheye_cameras = []
                        removal_cancelled = True
                
                # Now remove original cameras if user confirmed
                if fisheye_cameras:  # Only proceed if we have cameras to remove and user didn't cancel
                    self.status_label.setText("Removing original fisheye cameras...")
                    QtWidgets.QApplication.processEvents()
                    
                    # Check tie points before removal
                    projections_before = 0
                    valid_tie_points_before = 0
                    
                    if chunk.tie_points and chunk.tie_points.points:
                        for camera in chunk.cameras:
                            if camera in chunk.tie_points.projections:
                                projections_before += len(chunk.tie_points.projections[camera])
                        valid_tie_points_before = len([pt for pt in chunk.tie_points.points if pt.valid])
                        log("Tie points before removal: {} projections, {} valid points".format(
                            projections_before, valid_tie_points_before))
                    
                    # Remove the fisheye cameras
                    removed_count = len(fisheye_cameras)
                    chunk.remove(fisheye_cameras)
                    log("Removed {} fisheye cameras".format(removed_count))
                    
                    # Check tie points after removal
                    if chunk.tie_points and chunk.tie_points.points:
                        projections_after = 0
                        for camera in chunk.cameras:
                            if camera in chunk.tie_points.projections:
                                projections_after += len(chunk.tie_points.projections[camera])
                        
                        valid_tie_points_after = len([pt for pt in chunk.tie_points.points if pt.valid])
                        log("Tie points after removal: {} projections, {} valid points".format(
                            projections_after, valid_tie_points_after))
                    else:
                        projections_after = 0
                        valid_tie_points_after = 0
                        log("Tie points after removal: No tie points remaining")
                    
                    if valid_tie_points_after == 0 and valid_tie_points_before > 0:
                        log("As expected, tie points were removed with the fisheye cameras.")
                        log("Pinhole cameras retain their positions from the original alignment.")
                        
                        # Check if user wants automatic realignment
                        if self.realign_after_delete_cb.isChecked():
                            self.status_label.setText("Creating new tie points for pinhole cameras...")
                            QtWidgets.QApplication.processEvents()
                            
                            try:
                                log("Creating tie points for {} pinhole cameras...".format(len(pinhole_cameras)))
                                log("Pinhole views contain the same image data as fisheyes, just undistorted")
                                
                                # Verify pinhole cameras are properly set up
                                enabled_count = 0
                                aligned_count = 0
                                for cam in pinhole_cameras:
                                    if not cam.enabled:
                                        cam.enabled = True
                                        log("Enabled camera: {}".format(cam.label))
                                    enabled_count += 1
                                    
                                    if cam.transform:
                                        aligned_count += 1
                                    else:
                                        log("WARNING: Camera {} has no transform!".format(cam.label))
                                
                                log("{} pinhole cameras enabled, {} have valid transforms".format(
                                    enabled_count, aligned_count))
                                
                                if aligned_count == 0:
                                    log("ERROR: No pinhole cameras have valid transforms!")
                                    stats['errors'].append("No pinhole cameras have valid transforms")
                                    return
                                
                                # IMPORTANT: We need to detect keypoints first, then match them
                                # The issue is that newly added cameras don't have keypoints yet
                                
                                # Check total cameras in chunk
                                total_enabled = sum(1 for cam in chunk.cameras if cam.enabled)
                                log("Total enabled cameras in chunk: {}".format(total_enabled))
                                
                                # The simplest approach - just run matchPhotos on all enabled cameras
                                log("Running Match Photos on all {} enabled cameras...".format(total_enabled))
                                log("Using Metashape default settings for best compatibility")
                                
                                # Run match photos with standard parameters
                                log("Starting Match Photos process...")
                                chunk.matchPhotos(
                                    downscale=1,  # Half resolution
                                    generic_preselection=True,
                                    reference_preselection=False,
                                    filter_mask=True,
                                    mask_tiepoints=True,
                                    filter_stationary_points=True,
                                    keypoint_limit=40000,
                                    tiepoint_limit=4000,
                                    guided_matching=False,
                                    reset_matches=True,
                                    keep_keypoints=True
                                )
                                
                                log("Match Photos completed")
                                
                                # Step 2: Do NOT align cameras - preserve existing poses!
                                log("Preserving camera poses from fisheye alignment")
                                log("Tie points created without modifying camera positions")
                                
                                # Count new tie points
                                if chunk.tie_points and chunk.tie_points.points:
                                    new_valid_points = len([pt for pt in chunk.tie_points.points if pt.valid])
                                    log("Created {} new tie points for pinhole cameras".format(new_valid_points))
                                    stats['errors'].append("Created {} new tie points for pinhole views.".format(new_valid_points))
                                else:
                                    log("Failed to create new tie points")
                                    stats['errors'].append("Failed to create new tie points. Manual alignment may be needed.")
                                    
                            except Exception as realign_error:
                                log("ERROR: Failed to create new tie points: {}".format(str(realign_error)))
                                stats['errors'].append("Failed to create new tie points: {}".format(str(realign_error)))
                        else:
                            stats['errors'].append("Tie points were removed. Run 'Workflow > Align Photos' to create new tie points for pinhole views.")
            
            # Only refine alignment if:
            # 1. The option is checked
            # 2. We have new cameras
            # 3. We didn't cancel fisheye removal (or we're not deleting fisheyes at all)
            if (self.refine_alignment_cb.isChecked() and 
                stats.get('new_cameras') and 
                not (self.delete_originals_cb.isChecked() and removal_cancelled)):
                
                # Check if we have tie points before trying to optimize
                has_tie_points = False
                if chunk.tie_points and chunk.tie_points.points:
                    valid_points = len([pt for pt in chunk.tie_points.points if pt.valid])
                    has_tie_points = valid_points > 0
                
                if not has_tie_points:
                    log("WARNING: Cannot refine alignment without tie points!")
                    log("Skipping alignment refinement to preserve camera poses")
                    stats['errors'].append("Alignment refinement skipped - no tie points available")
                else:
                    self.status_label.setText("Refining alignment for new cameras...")
                    QtWidgets.QApplication.processEvents()
                    
                    try:
                        # Run optimizeCameras to refine positions without resetting
                        log("Optimizing camera positions with {} tie points...".format(valid_points))
                        # Note: optimizeCameras doesn't take a cameras parameter
                        # It optimizes all aligned cameras in the chunk
                        chunk.optimizeCameras(
                            fit_f=False,      # Don't change focal length
                            fit_cx=False,     # Don't change principal point
                            fit_cy=False,
                            fit_b1=False,     # Don't change aspect ratio
                            fit_b2=False,
                            fit_k1=False,     # Don't change distortion
                            fit_k2=False,
                            fit_k3=False,
                            fit_k4=False,
                            fit_p1=False,
                            fit_p2=False,
                            adaptive_fitting=False
                        )
                        log("Camera optimization completed")
                    except Exception as e:
                        stats['errors'].append("Alignment refinement failed: {}".format(str(e)))
            
            # Show results
            message = "Processing complete!\n\n"
            message += "Cameras processed: {}\n".format(stats['processed'])
            if stats.get('skipped_existing', 0) > 0:
                message += "Cameras skipped (existing): {}\n".format(stats['skipped_existing'])
            message += "Cameras failed: {}\n".format(stats['skipped'])
            message += "Total time: {:.1f} seconds\n".format(stats['time'])
            message += "Processing rate: {:.1f} cameras/second\n".format(
                stats['processed'] / stats['time'] if stats['time'] > 0 else 0)
            
            if self.process_masks_cb.isChecked():
                message += "\nMask processing was enabled.\n"
            if self.refine_alignment_cb.isChecked():
                message += "Alignment refinement was performed.\n"
            if self.delete_originals_cb.isChecked() and self.realign_after_delete_cb.isChecked():
                message += "\nNOTE: Tie point creation between pinhole views may require\n"
                message += "manual parameter tuning in Workflow > Align Photos.\n"
            
            if stats['errors']:
                message += "\nErrors encountered:\n"
                for error in stats['errors'][:5]:  # Show first 5 errors
                    message += "- {}\n".format(error)
                if len(stats['errors']) > 5:
                    message += "... and {} more errors".format(len(stats['errors']) - 5)
                    
            QtWidgets.QMessageBox.information(self, "Complete", message)
            self.status_label.setText("Complete")
            self.progress_bar.setValue(100)
            
        def closeEvent(self, event):
            if self.process_thread and self.process_thread.isRunning():
                reply = QtWidgets.QMessageBox.question(
                    self, 'Confirm Exit',
                    "Processing is still running. Are you sure you want to exit?",
                    QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
                    QtWidgets.QMessageBox.No)
                    
                if reply == QtWidgets.QMessageBox.Yes:
                    self.stop_processing()
                    self.process_thread.wait(1000)
                    event.accept()
                else:
                    event.ignore()
            else:
                event.accept()

def show_gui():
    """Show the GUI dialog"""
    if not PYSIDE2_AVAILABLE:
        Metashape.app.messageBox("PySide2 is required for the GUI version. Please use the non-GUI script instead.")
        return
        
    app = QtWidgets.QApplication.instance()
    if app is None:
        app = QtWidgets.QApplication([])
        
    dialog = FisheyeConverterGUI()
    dialog.exec_()

def main():
    """Main entry point - show GUI if available, otherwise run console version"""
    if PYSIDE2_AVAILABLE:
        show_gui()
    else:
        print("PySide2 not available. Please install it or use the non-GUI version of this script.")

# Add menu item to Metashape
label = "Scripts/Fisheye to Pinhole Converter (Improved)"
Metashape.app.addMenuItem(label, main)

if __name__ == "__main__":
    main()