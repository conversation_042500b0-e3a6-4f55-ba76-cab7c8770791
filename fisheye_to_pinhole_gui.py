# Fisheye to Pinhole Conversion Script for Metashape with GUI
# Converts fisheye images to pinhole perspective views
# Does NOT modify original cameras

import Metashape
import os
import sys
import math
import numpy as np
import cv2
import time
from datetime import datetime

# Try to import GUI libraries
try:
    from PySide2 import QtGui, QtCore, QtWidgets
    USE_GUI = True
except ImportError:
    print("PySide2 not available. Running in console mode.")
    USE_GUI = False

# Checking compatibility
compatible_major_version = "2.2"
found_major_version = ".".join(Metashape.app.version.split('.')[:2])
if found_major_version != compatible_major_version:
    raise Exception("Incompatible Metashape version: {} != {}".format(found_major_version, compatible_major_version))

# Default perspective views (matching COLMAP script)
DEFAULT_VIEWS = [
    {"name": "view_p45_r35", "yaw": 0.0, "pitch": 45.0, "roll": 35.3},
    {"name": "view_n45_r35", "yaw": 0.0, "pitch": -45.0, "roll": 35.3},
    {"name": "view_yL45_n54", "yaw": -45.0, "pitch": 0.0, "roll": -54.7}
]

class FisheyeConverter:
    def __init__(self):
        self.output_size = 1920
        self.fov_deg = 90.0
        self.jpeg_quality = 95
        self.process_all_cameras = True
        self.create_masks = False
        
    def calculate_rotation_matrix(self, yaw_deg, pitch_deg, roll_deg):
        """Calculate rotation matrix from Euler angles (ZYX convention)"""
        yaw = math.radians(yaw_deg)
        pitch = math.radians(pitch_deg)
        roll = math.radians(roll_deg)
        
        # Individual rotation matrices
        Rz = np.array([
            [math.cos(yaw), -math.sin(yaw), 0],
            [math.sin(yaw), math.cos(yaw), 0],
            [0, 0, 1]
        ])
        
        Ry = np.array([
            [math.cos(pitch), 0, math.sin(pitch)],
            [0, 1, 0],
            [-math.sin(pitch), 0, math.cos(pitch)]
        ])
        
        Rx = np.array([
            [1, 0, 0],
            [0, math.cos(roll), -math.sin(roll)],
            [0, math.sin(roll), math.cos(roll)]
        ])
        
        # Combined rotation (ZYX order)
        R = Rz @ Ry @ Rx
        return R

    def extract_calibration(self, camera):
        """Extract calibration parameters from camera"""
        sensor = camera.sensor
        if not sensor:
            return None
            
        calib = sensor.calibration
        width = sensor.width
        height = sensor.height
        
        if not calib:
            # Default calibration
            return {
                'width': width,
                'height': height,
                'fx': width / 2.0,
                'fy': width / 2.0,
                'cx': width / 2.0,
                'cy': height / 2.0,
                'k1': 0, 'k2': 0, 'k3': 0, 'k4': 0
            }
        
        # Extract parameters
        fx = calib.f if hasattr(calib, 'f') else width / 2.0
        fy = fx * (1 + calib.b1) if hasattr(calib, 'b1') else fx
        cx = width / 2.0 + (calib.cx if hasattr(calib, 'cx') else 0)
        cy = height / 2.0 + (calib.cy if hasattr(calib, 'cy') else 0)
        
        k1 = calib.k1 if hasattr(calib, 'k1') else 0
        k2 = calib.k2 if hasattr(calib, 'k2') else 0
        k3 = calib.k3 if hasattr(calib, 'k3') else 0
        k4 = calib.k4 if hasattr(calib, 'k4') else 0
        
        return {
            'width': width,
            'height': height,
            'fx': fx,
            'fy': fy,
            'cx': cx,
            'cy': cy,
            'k1': k1,
            'k2': k2,
            'k3': k3,
            'k4': k4
        }

    def dewarp_fisheye_to_pinhole(self, image, fisheye_calib, view_rotation):
        """Convert fisheye image to pinhole perspective"""
        # Fisheye camera matrix
        K_fisheye = np.array([
            [fisheye_calib['fx'], 0, fisheye_calib['cx']],
            [0, fisheye_calib['fy'], fisheye_calib['cy']],
            [0, 0, 1]
        ], dtype=np.float32)
        
        # Distortion coefficients
        D_fisheye = np.array([
            fisheye_calib['k1'],
            fisheye_calib['k2'],
            fisheye_calib['k3'],
            fisheye_calib['k4']
        ], dtype=np.float32)
        
        # Pinhole camera matrix
        fov_rad = math.radians(self.fov_deg)
        fx = (self.output_size / 2.0) / math.tan(fov_rad / 2.0)
        fy = fx
        cx = self.output_size / 2.0
        cy = self.output_size / 2.0
        
        K_pinhole = np.array([
            [fx, 0, cx],
            [0, fy, cy],
            [0, 0, 1]
        ], dtype=np.float32)
        
        # Generate undistortion maps
        map1, map2 = cv2.fisheye.initUndistortRectifyMap(
            K_fisheye,
            D_fisheye,
            view_rotation.astype(np.float32),
            K_pinhole,
            (self.output_size, self.output_size),
            cv2.CV_32FC1
        )
        
        # Remap the image
        dewarped = cv2.remap(image, map1, map2, cv2.INTER_LINEAR,
                           borderMode=cv2.BORDER_CONSTANT, borderValue=(0, 0, 0))
        
        return dewarped, (fx, fy, cx, cy), (map1, map2)

    def process_cameras(self, chunk, output_dir, progress_callback=None):
        """Process cameras and return info for adding to chunk"""
        # Create output directory
        images_dir = os.path.join(output_dir, "images")
        os.makedirs(images_dir, exist_ok=True)
        
        masks_dir = None
        if self.create_masks:
            masks_dir = os.path.join(output_dir, "masks")
            os.makedirs(masks_dir, exist_ok=True)
        
        # Find cameras to process
        cameras_to_process = []
        for camera in chunk.cameras:
            if camera.transform and camera.photo:
                cameras_to_process.append(camera)
        
        if not cameras_to_process:
            raise Exception("No aligned cameras found")
            
        print("Found {} aligned cameras to process".format(len(cameras_to_process)))
        
        # Process each camera
        new_camera_info = []
        total_ops = len(cameras_to_process) * len(DEFAULT_VIEWS)
        current_op = 0
        
        for cam_idx, camera in enumerate(cameras_to_process):
            print("\nProcessing: {} ({}/{})".format(
                camera.label, cam_idx + 1, len(cameras_to_process)))
            
            # Get calibration
            calib = self.extract_calibration(camera)
            if not calib:
                print("  Skipping - no calibration")
                continue
                
            # Load image
            if not os.path.exists(camera.photo.path):
                print("  Skipping - image not found: {}".format(camera.photo.path))
                continue
                
            image = cv2.imread(camera.photo.path)
            if image is None:
                print("  Skipping - failed to load image")
                continue
            
            # Get camera pose
            T = camera.transform
            
            # Process each view
            for view in DEFAULT_VIEWS:
                current_op += 1
                if progress_callback:
                    progress_callback(current_op, total_ops)
                
                print("  Creating: {}".format(view['name']))
                
                # Calculate view rotation
                R_view = self.calculate_rotation_matrix(view['yaw'], view['pitch'], view['roll'])
                
                # Dewarp image
                dewarped, (fx, fy, cx, cy), (map1, map2) = self.dewarp_fisheye_to_pinhole(
                    image, calib, R_view)
                
                # Save image
                base_name = os.path.splitext(os.path.basename(camera.photo.path))[0]
                new_name = "{}_{}".format(base_name, view['name'])
                new_path = os.path.join(images_dir, new_name + ".jpg")
                cv2.imwrite(new_path, dewarped, [cv2.IMWRITE_JPEG_QUALITY, self.jpeg_quality])
                
                # Create mask if requested
                mask_path = None
                if self.create_masks:
                    # Create mask showing valid pixels
                    mask = np.zeros((self.output_size, self.output_size), dtype=np.uint8)
                    valid_pixels = (map1 >= 0) & (map1 < calib['width']) & \
                                 (map2 >= 0) & (map2 < calib['height'])
                    mask[valid_pixels] = 255
                    mask_path = os.path.join(masks_dir, new_name + "_mask.png")
                    cv2.imwrite(mask_path, mask)
                
                # Calculate new camera transform
                R_cam = Metashape.Matrix([
                    [T[0,0], T[0,1], T[0,2]],
                    [T[1,0], T[1,1], T[1,2]],
                    [T[2,0], T[2,1], T[2,2]]
                ])
                
                R_view_meta = Metashape.Matrix([
                    [R_view[0,0], R_view[0,1], R_view[0,2]],
                    [R_view[1,0], R_view[1,1], R_view[1,2]],
                    [R_view[2,0], R_view[2,1], R_view[2,2]]
                ])
                
                # New camera looks in direction R_cam * R_view
                R_new = R_cam * R_view_meta
                
                # Position stays the same
                pos = camera.center
                
                # Store info for later
                new_camera_info.append({
                    'path': new_path,
                    'label': new_name,
                    'sensor_group': camera.sensor.label if camera.sensor else "Unknown",
                    'position': pos,
                    'rotation': R_new,
                    'focal': fx,
                    'cx': cx - self.output_size/2,  # Metashape wants offset from center
                    'cy': cy - self.output_size/2,
                    'mask_path': mask_path
                })
        
        return new_camera_info

def add_cameras_to_chunk(chunk, camera_info):
    """Add new cameras to chunk"""
    if not camera_info:
        return
    
    print("\nAdding {} new cameras to project...".format(len(camera_info)))
    
    # Group by sensor
    by_sensor = {}
    for info in camera_info:
        sg = info['sensor_group']
        if sg not in by_sensor:
            by_sensor[sg] = []
        by_sensor[sg].append(info)
    
    # Create sensors and add cameras
    for sensor_name, cameras in by_sensor.items():
        # Create pinhole sensor
        sensor = chunk.addSensor()
        sensor.label = "Pinhole from {}".format(sensor_name)
        sensor.type = Metashape.Sensor.Type.Frame
        sensor.width = cameras[0]['focal'] * 2  # Approximate from focal length
        sensor.height = cameras[0]['focal'] * 2
        sensor.focal_length = cameras[0]['focal']
        
        # Get actual output size (all should be same)
        test_img = cv2.imread(cameras[0]['path'])
        if test_img is not None:
            h, w = test_img.shape[:2]
            sensor.width = w
            sensor.height = h
        
        # Set calibration
        calib = Metashape.Calibration()
        calib.width = sensor.width
        calib.height = sensor.height
        calib.f = cameras[0]['focal']
        calib.cx = cameras[0]['cx']
        calib.cy = cameras[0]['cy']
        calib.k1 = 0
        calib.k2 = 0
        calib.k3 = 0
        sensor.calibration = calib
        
        print("Created sensor: {}".format(sensor.label))
        
        # Add photos - IMPORTANT: Do this all at once to avoid duplicates
        paths = [c['path'] for c in cameras]
        chunk.addPhotos(paths)
        
        # Configure each camera - get the last N cameras added
        total_cams = len(chunk.cameras)
        cam_start = total_cams - len(cameras)
        
        for i, info in enumerate(cameras):
            cam = chunk.cameras[cam_start + i]
            cam.label = info['label']
            cam.sensor = sensor
            
            # Set transform
            R = info['rotation']
            t = info['position']
            T = Metashape.Matrix([
                [R[0,0], R[0,1], R[0,2], t.x],
                [R[1,0], R[1,1], R[1,2], t.y],
                [R[2,0], R[2,1], R[2,2], t.z],
                [0, 0, 0, 1]
            ])
            cam.transform = T
            
            # Add mask if available
            if info.get('mask_path') and os.path.exists(info['mask_path']):
                mask = Metashape.Mask()
                mask.load(info['mask_path'])
                cam.mask = mask
    
    print("Successfully added all cameras")

# GUI Implementation
if USE_GUI:
    class ProcessingThread(QtCore.QThread):
        progress = QtCore.Signal(int, int)
        finished = QtCore.Signal(list)
        error = QtCore.Signal(str)
        
        def __init__(self, converter, chunk, output_dir):
            super().__init__()
            self.converter = converter
            self.chunk = chunk
            self.output_dir = output_dir
            
        def run(self):
            try:
                def progress_callback(current, total):
                    self.progress.emit(current, total)
                
                result = self.converter.process_cameras(
                    self.chunk, self.output_dir, progress_callback)
                self.finished.emit(result)
            except Exception as e:
                self.error.emit(str(e))
    
    class FisheyeConverterDialog(QtWidgets.QDialog):
        def __init__(self, parent=None):
            super().__init__(parent)
            self.converter = FisheyeConverter()
            self.camera_info = None
            self.init_ui()
            
        def init_ui(self):
            self.setWindowTitle("Fisheye to Pinhole Converter")
            self.setMinimumWidth(400)
            
            layout = QtWidgets.QVBoxLayout()
            
            # Output directory
            dir_group = QtWidgets.QGroupBox("Output Directory")
            dir_layout = QtWidgets.QHBoxLayout()
            self.dir_edit = QtWidgets.QLineEdit()
            self.dir_button = QtWidgets.QPushButton("Browse...")
            self.dir_button.clicked.connect(self.browse_directory)
            dir_layout.addWidget(self.dir_edit)
            dir_layout.addWidget(self.dir_button)
            dir_group.setLayout(dir_layout)
            layout.addWidget(dir_group)
            
            # Settings
            settings_group = QtWidgets.QGroupBox("Settings")
            settings_layout = QtWidgets.QGridLayout()
            
            settings_layout.addWidget(QtWidgets.QLabel("Output Size:"), 0, 0)
            self.size_spin = QtWidgets.QSpinBox()
            self.size_spin.setRange(512, 4096)
            self.size_spin.setValue(self.converter.output_size)
            self.size_spin.setSingleStep(256)
            settings_layout.addWidget(self.size_spin, 0, 1)
            
            settings_layout.addWidget(QtWidgets.QLabel("Field of View:"), 1, 0)
            self.fov_spin = QtWidgets.QDoubleSpinBox()
            self.fov_spin.setRange(30.0, 120.0)
            self.fov_spin.setValue(self.converter.fov_deg)
            self.fov_spin.setSuffix("°")
            settings_layout.addWidget(self.fov_spin, 1, 1)
            
            settings_layout.addWidget(QtWidgets.QLabel("JPEG Quality:"), 2, 0)
            self.quality_spin = QtWidgets.QSpinBox()
            self.quality_spin.setRange(50, 100)
            self.quality_spin.setValue(self.converter.jpeg_quality)
            settings_layout.addWidget(self.quality_spin, 2, 1)
            
            self.mask_check = QtWidgets.QCheckBox("Create masks for valid areas")
            self.mask_check.setChecked(self.converter.create_masks)
            settings_layout.addWidget(self.mask_check, 3, 0, 1, 2)
            
            settings_group.setLayout(settings_layout)
            layout.addWidget(settings_group)
            
            # Info
            info_group = QtWidgets.QGroupBox("Information")
            info_layout = QtWidgets.QVBoxLayout()
            self.info_label = QtWidgets.QLabel()
            self.update_info()
            info_layout.addWidget(self.info_label)
            info_group.setLayout(info_layout)
            layout.addWidget(info_group)
            
            # Progress
            self.progress_bar = QtWidgets.QProgressBar()
            self.progress_bar.setVisible(False)
            layout.addWidget(self.progress_bar)
            
            # Buttons
            button_layout = QtWidgets.QHBoxLayout()
            self.process_button = QtWidgets.QPushButton("Process Images")
            self.process_button.clicked.connect(self.process_images)
            self.add_button = QtWidgets.QPushButton("Add to Project")
            self.add_button.clicked.connect(self.add_to_project)
            self.add_button.setEnabled(False)
            self.close_button = QtWidgets.QPushButton("Close")
            self.close_button.clicked.connect(self.reject)
            
            button_layout.addWidget(self.process_button)
            button_layout.addWidget(self.add_button)
            button_layout.addWidget(self.close_button)
            layout.addLayout(button_layout)
            
            self.setLayout(layout)
            
        def browse_directory(self):
            dir_path = QtWidgets.QFileDialog.getExistingDirectory(
                self, "Select Output Directory")
            if dir_path:
                self.dir_edit.setText(dir_path)
                
        def update_info(self):
            chunk = Metashape.app.document.chunk
            if chunk:
                aligned = sum(1 for c in chunk.cameras if c.transform)
                total = len(chunk.cameras)
                self.info_label.setText(
                    "Cameras: {}/{} aligned\n"
                    "Each camera will generate 3 perspective views".format(aligned, total))
            else:
                self.info_label.setText("No active chunk")
                
        def update_settings(self):
            self.converter.output_size = self.size_spin.value()
            self.converter.fov_deg = self.fov_spin.value()
            self.converter.jpeg_quality = self.quality_spin.value()
            self.converter.create_masks = self.mask_check.isChecked()
            
        def process_images(self):
            output_dir = self.dir_edit.text()
            if not output_dir:
                QtWidgets.QMessageBox.warning(
                    self, "Warning", "Please select an output directory")
                return
                
            chunk = Metashape.app.document.chunk
            if not chunk:
                QtWidgets.QMessageBox.warning(
                    self, "Warning", "No active chunk found")
                return
            
            self.update_settings()
            
            # Disable UI
            self.process_button.setEnabled(False)
            self.add_button.setEnabled(False)
            self.progress_bar.setVisible(True)
            
            # Start processing
            self.thread = ProcessingThread(self.converter, chunk, output_dir)
            self.thread.progress.connect(self.update_progress)
            self.thread.finished.connect(self.processing_finished)
            self.thread.error.connect(self.processing_error)
            self.thread.start()
            
        def update_progress(self, current, total):
            self.progress_bar.setMaximum(total)
            self.progress_bar.setValue(current)
            
        def processing_finished(self, camera_info):
            self.camera_info = camera_info
            self.progress_bar.setVisible(False)
            self.process_button.setEnabled(True)
            self.add_button.setEnabled(True)
            
            QtWidgets.QMessageBox.information(
                self, "Success", 
                "Processed {} images successfully!".format(len(camera_info)))
                
        def processing_error(self, error_msg):
            self.progress_bar.setVisible(False)
            self.process_button.setEnabled(True)
            QtWidgets.QMessageBox.critical(
                self, "Error", "Processing failed:\n{}".format(error_msg))
                
        def add_to_project(self):
            if not self.camera_info:
                return
                
            chunk = Metashape.app.document.chunk
            if not chunk:
                QtWidgets.QMessageBox.warning(
                    self, "Warning", "No active chunk found")
                return
            
            try:
                add_cameras_to_chunk(chunk, self.camera_info)
                # NOT SAVING - User will save manually when ready
                QtWidgets.QMessageBox.information(
                    self, "Success", 
                    "Added {} cameras to project\n\n"
                    "Project NOT saved - save manually when ready".format(len(self.camera_info)))
                self.accept()
            except Exception as e:
                QtWidgets.QMessageBox.critical(
                    self, "Error", "Failed to add cameras:\n{}".format(str(e)))

# Console mode
def run_console():
    converter = FisheyeConverter()
    
    chunk = Metashape.app.document.chunk
    if not chunk:
        print("No active chunk")
        return
        
    output_dir = Metashape.app.getExistingDirectory("Select output directory")
    if not output_dir:
        return
    
    # Get settings
    labels = ["Output size:", "Field of view:", "JPEG quality:"]
    values = [str(converter.output_size), str(converter.fov_deg), str(converter.jpeg_quality)]
    
    result = Metashape.app.getMultipleStrings("Settings", labels, values)
    if result:
        try:
            converter.output_size = int(result[0])
            converter.fov_deg = float(result[1])
            converter.jpeg_quality = int(result[2])
        except:
            print("Invalid settings")
            return
    
    print("Processing...")
    try:
        camera_info = converter.process_cameras(chunk, output_dir)
        print("Processed {} images".format(len(camera_info)))
        
        if Metashape.app.getBool("Add cameras to project?"):
            add_cameras_to_chunk(chunk, camera_info)
            # NOT SAVING - User will save manually when ready
            print("Added cameras to project (NOT saved - save manually when ready)")
            
    except Exception as e:
        print("Error: {}".format(str(e)))

# Main
def main():
    if USE_GUI:
        app = QtWidgets.QApplication.instance()
        parent = app.activeWindow()
        dialog = FisheyeConverterDialog(parent)
        dialog.exec_()
    else:
        run_console()

if __name__ == "__main__":
    main()