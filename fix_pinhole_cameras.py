# Fix script for pinhole cameras that are unaligned and in "unknown" group
import Metashape
import math

doc = Metashape.app.document
chunk = doc.chunk

if not chunk:
    print("No active chunk")
else:
    # Parameters
    output_size = 1920
    fov_deg = 90.0
    fov_rad = math.radians(fov_deg)
    focal = (output_size / 2.0) / math.tan(fov_rad / 2.0)
    
    # Find or create proper sensors
    sensors_created = {}
    
    # Fix cameras
    fixed_count = 0
    
    for camera in chunk.cameras:
        # Check if this is a pinhole camera that needs fixing
        if "view_" in camera.label and (not camera.sensor or camera.sensor.label == "unknown"):
            # Determine which sensor group it should belong to
            if "back" in camera.label.lower():
                sensor_name = "Pinhole_Back"
            elif "front" in camera.label.lower():
                sensor_name = "Pinhole_Front"
            else:
                print("Can't determine sensor for: {}".format(camera.label))
                continue
            
            # Create sensor if needed
            if sensor_name not in sensors_created:
                # Look for existing sensor first
                sensor = None
                for s in chunk.sensors:
                    if s.label == sensor_name:
                        sensor = s
                        break
                
                if not sensor:
                    # Create new sensor
                    sensor = chunk.addSensor()
                    sensor.label = sensor_name
                    sensor.type = Metashape.Sensor.Type.Frame
                    sensor.width = output_size
                    sensor.height = output_size
                    sensor.focal_length = focal
                    sensor.fixed = True
                    
                    # Set calibration
                    calib = Metashape.Calibration()
                    calib.type = Metashape.Sensor.Type.Frame
                    calib.width = output_size
                    calib.height = output_size
                    calib.f = focal
                    calib.cx = 0
                    calib.cy = 0
                    calib.b1 = 0
                    calib.k1 = 0
                    calib.k2 = 0
                    calib.k3 = 0
                    calib.k4 = 0
                    calib.p1 = 0
                    calib.p2 = 0
                    sensor.calibration = calib
                    
                    print("Created sensor: {}".format(sensor_name))
                
                sensors_created[sensor_name] = sensor
            
            # Assign sensor
            camera.sensor = sensors_created[sensor_name]
            fixed_count += 1
            print("Fixed: {} -> {}".format(camera.label, sensor_name))
    
    print("\nFixed {} cameras".format(fixed_count))
    
    # Now fix alignment - find corresponding original camera
    alignment_fixed = 0
    
    for camera in chunk.cameras:
        if "view_" in camera.label and not camera.transform:
            # Extract original camera name
            parts = camera.label.split("_view_")
            if len(parts) == 2:
                original_name = parts[0]
                view_part = "view_" + parts[1]
                
                # Find original camera
                original_cam = None
                for c in chunk.cameras:
                    if c.label == original_name:
                        original_cam = c
                        break
                
                if original_cam and original_cam.transform:
                    # Extract view parameters
                    if "p45_r35" in view_part:
                        yaw, pitch, roll = 0.0, 45.0, 35.3
                    elif "n45_r35" in view_part:
                        yaw, pitch, roll = 0.0, -45.0, 35.3
                    elif "yL45_n54" in view_part:
                        yaw, pitch, roll = -45.0, 0.0, -54.7
                    else:
                        print("Unknown view type: {}".format(view_part))
                        continue
                    
                    # Calculate rotation
                    import numpy as np
                    yaw_r = math.radians(yaw)
                    pitch_r = math.radians(pitch)
                    roll_r = math.radians(roll)
                    
                    Rz = np.array([
                        [math.cos(yaw_r), -math.sin(yaw_r), 0],
                        [math.sin(yaw_r), math.cos(yaw_r), 0],
                        [0, 0, 1]
                    ])
                    
                    Ry = np.array([
                        [math.cos(pitch_r), 0, math.sin(pitch_r)],
                        [0, 1, 0],
                        [-math.sin(pitch_r), 0, math.cos(pitch_r)]
                    ])
                    
                    Rx = np.array([
                        [1, 0, 0],
                        [0, math.cos(roll_r), -math.sin(roll_r)],
                        [0, math.sin(roll_r), math.cos(roll_r)]
                    ])
                    
                    R_view = Rz @ Ry @ Rx
                    
                    # Get original transform
                    T_orig = original_cam.transform
                    R_orig = Metashape.Matrix([
                        [T_orig[0,0], T_orig[0,1], T_orig[0,2]],
                        [T_orig[1,0], T_orig[1,1], T_orig[1,2]],
                        [T_orig[2,0], T_orig[2,1], T_orig[2,2]]
                    ])
                    
                    R_view_meta = Metashape.Matrix([
                        [R_view[0,0], R_view[0,1], R_view[0,2]],
                        [R_view[1,0], R_view[1,1], R_view[1,2]],
                        [R_view[2,0], R_view[2,1], R_view[2,2]]
                    ])
                    
                    # New rotation
                    R_new = R_orig * R_view_meta
                    
                    # Same position as original
                    pos = original_cam.center
                    
                    # Build transform
                    T_new = Metashape.Matrix([
                        [R_new[0,0], R_new[0,1], R_new[0,2], pos.x],
                        [R_new[1,0], R_new[1,1], R_new[1,2], pos.y],
                        [R_new[2,0], R_new[2,1], R_new[2,2], pos.z],
                        [0, 0, 0, 1]
                    ])
                    
                    camera.transform = T_new
                    alignment_fixed += 1
                    print("Aligned: {}".format(camera.label))
                else:
                    print("Could not find original camera for: {}".format(camera.label))
    
    print("\nAligned {} cameras".format(alignment_fixed))
    print("\nDone! Remember to save if the fixes look correct.")