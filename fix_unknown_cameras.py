# Quick fix script to reassign unknown cameras to correct sensors
import Metashape

doc = Metashape.app.document
chunk = doc.chunk

if not chunk:
    print("No active chunk")
else:
    # Find sensors
    back_sensor = None
    front_sensor = None
    
    for sensor in chunk.sensors:
        if sensor.label == "Back":
            back_sensor = sensor
        elif sensor.label == "Front":
            front_sensor = sensor
    
    # Create sensors if missing
    if not back_sensor:
        back_sensor = chunk.addSensor()
        back_sensor.label = "Back"
        print("Created Back sensor")
        
    if not front_sensor:
        front_sensor = chunk.addSensor()
        front_sensor.label = "Front"
        print("Created Front sensor")
    
    # Fix unknown cameras
    fixed_count = 0
    
    for camera in chunk.cameras:
        if not camera.sensor or camera.sensor.label == "unknown":
            # Check camera name
            if "back" in camera.label.lower():
                camera.sensor = back_sensor
                fixed_count += 1
                print("Fixed: {} -> Back".format(camera.label))
            elif "front" in camera.label.lower():
                camera.sensor = front_sensor
                fixed_count += 1
                print("Fixed: {} -> Front".format(camera.label))
    
    print("\nFixed {} cameras".format(fixed_count))
    print("Remember to save if the fix looks correct!")