# Fisheye to Pinhole - Fixed Final Version
# Correctly applies rotations and processes without interruptions

import Metashape
import os
import math
import numpy as np
import cv2
import time

# Default views matching COLMAP script
VIEWS = [
    {"name": "view_p45_r35", "yaw": 0.0, "pitch": 45.0, "roll": 35.3},
    {"name": "view_n45_r35", "yaw": 0.0, "pitch": -45.0, "roll": 35.3},
    {"name": "view_yL45_n54", "yaw": -45.0, "pitch": 0.0, "roll": -54.7}
]

def log(msg):
    print("[{}] {}".format(time.strftime("%H:%M:%S"), msg))

def calculate_rotation_matrix(yaw_deg, pitch_deg, roll_deg):
    """Calculate rotation matrix from Euler angles (ZYX convention)"""
    yaw = math.radians(yaw_deg)
    pitch = math.radians(pitch_deg)
    roll = math.radians(roll_deg)
    
    # Individual rotation matrices
    Rz = np.array([
        [math.cos(yaw), -math.sin(yaw), 0],
        [math.sin(yaw), math.cos(yaw), 0],
        [0, 0, 1]
    ])
    
    Ry = np.array([
        [math.cos(pitch), 0, math.sin(pitch)],
        [0, 1, 0],
        [-math.sin(pitch), 0, math.cos(pitch)]
    ])
    
    Rx = np.array([
        [1, 0, 0],
        [0, math.cos(roll), -math.sin(roll)],
        [0, math.sin(roll), math.cos(roll)]
    ])
    
    # Combined rotation (ZYX order)
    R = Rz @ Ry @ Rx
    return R

def process_single_camera(camera, output_dir, output_size, fov_deg, chunk, sensor_cache):
    """Process a single camera and add its views to the chunk immediately"""
    
    log("Processing camera: {}".format(camera.label))
    
    # Get calibration
    sensor = camera.sensor
    if not sensor:
        log("  ERROR: No sensor")
        return []
    
    calib = sensor.calibration
    if not calib:
        log("  ERROR: No calibration")
        return []
    
    # Extract calibration parameters
    width = sensor.width
    height = sensor.height
    
    # Get focal length and principal point
    fx = calib.f if hasattr(calib, 'f') else width/2
    fy = fx * (1 + calib.b1 if hasattr(calib, 'b1') else 0)
    cx = width/2 + (calib.cx if hasattr(calib, 'cx') else 0)
    cy = height/2 + (calib.cy if hasattr(calib, 'cy') else 0)
    
    # Get distortion
    k1 = calib.k1 if hasattr(calib, 'k1') else 0
    k2 = calib.k2 if hasattr(calib, 'k2') else 0
    k3 = calib.k3 if hasattr(calib, 'k3') else 0
    k4 = calib.k4 if hasattr(calib, 'k4') else 0
    
    # Load image
    if not os.path.exists(camera.photo.path):
        log("  ERROR: Image not found: {}".format(camera.photo.path))
        return []
    
    image = cv2.imread(camera.photo.path)
    if image is None:
        log("  ERROR: Failed to load image")
        return []
    
    # Create or get pinhole sensor
    sensor_key = "Pinhole_{}".format(sensor.label)
    if sensor_key not in sensor_cache:
        # Create new pinhole sensor
        pinhole_sensor = chunk.addSensor()
        pinhole_sensor.label = sensor_key
        pinhole_sensor.type = Metashape.Sensor.Type.Frame
        pinhole_sensor.width = output_size
        pinhole_sensor.height = output_size
        
        # Calculate focal length for pinhole
        fov_rad = math.radians(fov_deg)
        pinhole_fx = (output_size / 2.0) / math.tan(fov_rad / 2.0)
        pinhole_sensor.focal_length = pinhole_fx
        
        # Set calibration
        pinhole_calib = Metashape.Calibration()
        pinhole_calib.type = Metashape.Sensor.Type.Frame
        pinhole_calib.width = output_size
        pinhole_calib.height = output_size
        pinhole_calib.f = pinhole_fx
        pinhole_calib.cx = 0  # Centered (Metashape uses offset from center)
        pinhole_calib.cy = 0
        pinhole_calib.b1 = 0  # No aspect ratio difference
        pinhole_calib.b2 = 0
        pinhole_calib.k1 = 0
        pinhole_calib.k2 = 0
        pinhole_calib.k3 = 0
        pinhole_calib.k4 = 0
        pinhole_calib.p1 = 0
        pinhole_calib.p2 = 0
        pinhole_sensor.calibration = pinhole_calib
        
        # Also set sensor properties
        pinhole_sensor.fixed = True  # Fix calibration
        pinhole_sensor.pixel_width = 1
        pinhole_sensor.pixel_height = 1
        pinhole_sensor.focal_length = pinhole_fx
        
        sensor_cache[sensor_key] = pinhole_sensor
        log("  Created sensor: {}".format(sensor_key))
    
    pinhole_sensor = sensor_cache[sensor_key]
    
    # Process each view
    added_cameras = []
    
    for view_idx, view in enumerate(VIEWS):
        log("  Processing view: {}".format(view['name']))
        
        # Calculate rotation for the view
        R_view = calculate_rotation_matrix(view['yaw'], view['pitch'], view['roll'])
        
        # Setup matrices
        K_fisheye = np.array([
            [fx, 0, cx],
            [0, fy, cy],
            [0, 0, 1]
        ], dtype=np.float32)
        
        D_fisheye = np.array([k1, k2, k3, k4], dtype=np.float32)
        
        fov_rad = math.radians(fov_deg)
        pinhole_f = (output_size / 2.0) / math.tan(fov_rad / 2.0)
        K_pinhole = np.array([
            [pinhole_f, 0, output_size/2],
            [0, pinhole_f, output_size/2],
            [0, 0, 1]
        ], dtype=np.float32)
        
        # Generate undistortion maps
        try:
            map1, map2 = cv2.fisheye.initUndistortRectifyMap(
                K_fisheye,
                D_fisheye,
                R_view.astype(np.float32),
                K_pinhole,
                (output_size, output_size),
                cv2.CV_32FC1
            )
        except Exception as e:
            log("    ERROR in undistortion: {}".format(str(e)))
            continue
        
        # Apply remapping
        dewarped = cv2.remap(image, map1, map2, cv2.INTER_LINEAR,
                           borderMode=cv2.BORDER_CONSTANT, borderValue=(0, 0, 0))
        
        # Save image
        base_name = os.path.splitext(os.path.basename(camera.photo.path))[0]
        new_name = "{}_{}".format(base_name, view['name'])
        new_path = os.path.join(output_dir, "images", new_name + ".jpg")
        cv2.imwrite(new_path, dewarped, [cv2.IMWRITE_JPEG_QUALITY, 95])
        
        # Add camera IMMEDIATELY
        chunk.addPhotos([new_path])
        new_cam = chunk.cameras[-1]  # Get the last added camera
        
        # Verify it's the right camera
        if new_cam.photo.path != new_path:
            log("    ERROR: Camera mismatch! Expected {} but got {}".format(
                new_path, new_cam.photo.path))
            continue
        
        # Configure camera
        new_cam.label = new_name
        new_cam.sensor = pinhole_sensor
        
        # Calculate transform
        # IMPORTANT: When we apply R_view to the image, we're rotating the viewing direction
        # So we need to apply the INVERSE rotation to the camera pose
        T_orig = camera.transform
        R_orig = Metashape.Matrix([
            [T_orig[0,0], T_orig[0,1], T_orig[0,2]],
            [T_orig[1,0], T_orig[1,1], T_orig[1,2]],
            [T_orig[2,0], T_orig[2,1], T_orig[2,2]]
        ])
        
        # Create inverse of view rotation (transpose for rotation matrix)
        R_view_inv = R_view.T
        R_view_inv_meta = Metashape.Matrix([
            [R_view_inv[0,0], R_view_inv[0,1], R_view_inv[0,2]],
            [R_view_inv[1,0], R_view_inv[1,1], R_view_inv[1,2]],
            [R_view_inv[2,0], R_view_inv[2,1], R_view_inv[2,2]]
        ])
        
        # New rotation: apply inverse view rotation in camera's local space
        R_new = R_orig * R_view_inv_meta
        
        # Same position
        pos = camera.center
        
        # Build transform
        T_new = Metashape.Matrix([
            [R_new[0,0], R_new[0,1], R_new[0,2], pos.x],
            [R_new[1,0], R_new[1,1], R_new[1,2], pos.y],
            [R_new[2,0], R_new[2,1], R_new[2,2], pos.z],
            [0, 0, 0, 1]
        ])
        
        new_cam.transform = T_new
        
        log("    Added camera: {}".format(new_cam.label))
        added_cameras.append(new_cam)
    
    return added_cameras

def main():
    doc = Metashape.app.document
    if not doc or not doc.chunk:
        print("No active document/chunk")
        return
        
    chunk = doc.chunk
    
    # Get output directory
    output_dir = Metashape.app.getExistingDirectory("Select output directory")
    if not output_dir:
        return
    
    # Create directories
    images_dir = os.path.join(output_dir, "images")
    os.makedirs(images_dir, exist_ok=True)
    
    # Parameters
    output_size = 1920
    fov_deg = 90.0
    
    # Find cameras to process
    cameras_to_process = []
    for camera in chunk.cameras:
        if camera.transform and camera.photo:
            # Skip if already processed
            if "view_" not in camera.label:
                cameras_to_process.append(camera)
    
    log("Found {} cameras to process".format(len(cameras_to_process)))
    
    if not cameras_to_process:
        log("No cameras to process")
        return
    
    # Process each camera individually
    sensor_cache = {}
    total_added = 0
    
    for idx, camera in enumerate(cameras_to_process):
        log("\n--- Camera {}/{} ---".format(idx + 1, len(cameras_to_process)))
        
        added = process_single_camera(camera, output_dir, output_size, fov_deg, chunk, sensor_cache)
        total_added += len(added)
    
    log("\nTotal cameras added: {}".format(total_added))
    log("DONE - Remember to save manually if results are correct!")

if __name__ == "__main__":
    main()