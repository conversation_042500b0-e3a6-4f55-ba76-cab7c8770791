# Fixes Applied to fisheye_to_pinhole_unified_improved.py

## 1. Fixed Focal Length Issue (3137 → 960)

The critical fix was adding:
```python
sensor.focal_length = pinhole_fx
```

Metashape requires BOTH `sensor.focal_length` AND `calibration.f` to be set. Without `sensor.focal_length`, Metashape uses a default value based on sensor dimensions.

Also added:
- `calib.type = Metashape.Sensor.Type.Frame`
- `sensor.pixel_width = 1` and `sensor.pixel_height = 1`
- Verification logging to confirm values are set correctly

## 2. Fixed Tie Points Disappearing When Deleting Fisheyes

- Removed the `matchPhotos` call that was creating new tie points
- Now only uses `alignCameras` to optimize positions
- Added new "Delete original fisheye cameras" option that properly removes cameras while preserving tie points using `chunk.remove(cameras)`
- Made disable/delete options mutually exclusive

## 3. COLMAP Export Issue

The focal length fix should resolve the black image issue when "transform to pinhole" is checked. The problem was that Metashape's COLMAP exporter was confused by the incorrect focal length.

## 4. Performance Improvements

- Removed excessive debug logging
- Changed undistortion maps to CV_16SC2 (faster than CV_32FC1)
- Made threading conditional - only uses threads for multiple views
- Simplified mask processing code

## How to Verify Fixes

1. **Focal Length**: Check the log output for:
   ```
   Created sensor 'Pinhole_Front' with focal length: 960.00 pixels (FOV: 90.0 deg)
     sensor.focal_length = 960.00
     sensor.calibration.f = 960.00
   ```

2. **Tie Points**: After processing with "Delete original fisheye cameras" checked, the tie point count should remain at ~41k

3. **COLMAP Export**: Export with "transform to pinhole" checked should produce valid images, not black ones

4. **Performance**: Processing should be noticeably faster with reduced logging overhead