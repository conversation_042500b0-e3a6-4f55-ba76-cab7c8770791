# Python scripts for Metashape Pro

These scripts are supposed to be used in Metashape Pro. They can be executed on Metashape launch automatically ([see article](https://agisoft.freshdesk.com/support/solutions/articles/31000133123-how-to-run-python-script-automatically-on-metashape-professional-start)).

# Contributions are welcome!

If you have script with general functionality that can be useful for other users - feel free to publish your script in this repository in directory [contrib](https://github.com/agisoft-llc/metashape-scripts/tree/master/src/contrib) via pull request.

# Compatibility with Metashape versions and PhotoScan

Each script checks that it is used with the proper Metashape version. Main branch of repository contains scripts compatible with last released version of Metashape.

If you need scripts for older releases of Metashape or PhotoScan - you can use scripts from the proper branch.
